{"name": "pc", "version": "1.12.1", "description": "中建前端项目", "main": "index.js", "scripts": {"dev": "vite --host 0.0.0.0 ", "build": "cross-env VUE_APP_AEGIS_ENV=pre NODE_OPTIONS='--max-old-space-size=6000' vite build --mode staging", "build:dev": "cross-env VUE_APP_AEGIS_ENV=development NODE_OPTIONS='--max-old-space-size=6000' vite build --mode staging", "build:test": "cross-env VUE_APP_AEGIS_ENV=test NODE_OPTIONS='--max-old-space-size=6000' vite build --mode staging", "build:prod": "cross-env VUE_APP_AEGIS_ENV=production NODE_OPTIONS='--max-old-space-size=6000' vite build", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test": "echo \"Error: no test specified\" && exit 1", "test:unit": "jest --clearCache && vue-cli-service test:unit", "lint": "eslint --fix --ext .js,.vue src --quiet", "test:ci": "npm run lint && npm run test:unit", "storybook": "cross-env VUE_APP_BASE_API=https://zjyth.youmatech.com storybook dev -p 6006 ", "build-storybook": "storybook build", "prepare": "husky install", "prebuild": "npm run check:node", "predev": "npm run check:node", "check:node": "node ./misc/checkNode"}, "repository": {"type": "git", "url": "************************:cscec-kfwy/web/pc.git"}, "author": "dandan", "license": "ISC", "dependencies": {"@antv/g2": "4.2.10", "@popperjs/core": "2.11.6", "ali-oss": "^6.22.0", "async-validator": "4.2.5", "axios": "1.3.6", "clipboard": "2.0.11", "core-js": "3.30.1", "cropperjs": "1.5.13", "echarts": "4.2.1", "element-zjui": "^1.1.28", "ezuikit-js": "0.6.5", "fuse.js": "6.6.2", "gcoord": "^1.0.7", "js-cookie": "3.0.4", "jsencrypt": "3.3.2", "jsonp": "0.2.1", "lodash": "4.17.21", "moment": "2.29.4", "normalize.css": "8.0.1", "nprogress": "0.2.0", "paper": "0.12.17", "path-to-regexp": "6.2.1", "print-js": "1.6.0", "qrcode": "1.5.1", "qs": "6.11.1", "recorder-core": "^1.2.23070100", "uuid": "9.0.0", "v-contextmenu": "2.9.0", "vue": "2.7.14", "vue-count-to": "^1.0.13", "vue-print-nb": "1.6.0", "vue-router": "3.0.2", "vue-runtime-helpers": "1.1.2", "vue-virtual-scroller": "^1.1.2", "vuedraggable": "2.24.3", "vuex": "3.1.0", "xgplayer": "^3.0.8"}, "devDependencies": {"@babel/core": "7.12.16", "@babel/polyfill": "7.12.1", "@storybook/addon-essentials": "7.1.0", "@storybook/addon-interactions": "7.1.0", "@storybook/addon-links": "7.1.0", "@storybook/addon-mdx-gfm": "7.1.0", "@storybook/blocks": "7.1.0", "@storybook/preset-scss": "1.0.3", "@storybook/testing-library": "0.2.0", "@storybook/vue": "7.1.0", "@storybook/vue-vite": "7.1.0", "@storybook/vue-webpack5": "7.1.0", "@vitejs/plugin-vue2": "2.2.0", "@vitejs/plugin-vue2-jsx": "1.1.0", "@vue/cli-plugin-babel": "5.0.8", "@vue/cli-plugin-eslint": "5.0.8", "@vue/cli-plugin-unit-jest": "5.0.8", "@vue/cli-service": "5.0.8", "@vue/eslint-config-standard": "6.1.0", "@vue/test-utils": "1.0.3", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-transform-remove-console": "6.9.4", "chalk": "2.4.2", "chokidar": "3.5.3", "compressing": "1.9.0", "compression-webpack-plugin": "10.0.0", "cross-env": "7.0.3", "eslint": "7.12.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.0", "eslint-plugin-storybook": "0.6.13", "eslint-plugin-vue": "7.20.0", "filemanager-webpack-plugin": "8.0.0", "html-webpack-plugin": "5.5.3", "husky": "7.0.4", "plop": "2.3.0", "react": "18.2.0", "react-dom": "18.2.0", "sass": "1.62.0", "sass-loader": "8.0.2", "semver": "7.5.4", "storybook": "7.1.0", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vite": "4.4.4", "vite-plugin-compression2": "0.10.3", "vite-plugin-externals": "0.6.2", "vite-plugin-html": "3.2.0", "vite-plugin-svg-icons": "2.0.1", "vue-template-compiler": "2.7.14", "webpack-strip-block": "0.3.0"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=18.16.0", "npm": ">= 9.5.0"}}
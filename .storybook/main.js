/** @type { import('@storybook/vue-vite').StorybookConfig } */
const path = require('path')
function resolve(dir) {
  return path.join(__dirname,'../', dir)
}

const config = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  logLevel: 'debug',
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-interactions",
    "@storybook/addon-mdx-gfm",
    "@storybook/preset-scss"
  ],
  framework: {
    name: "@storybook/vue-webpack5",
    options: {}
  },
  docs: {
    autodocs: "tag"
  },
  webpackFinal:config=>{

    console.log(config,999)
    config.resolve.alias['@'] = resolve('src')

    // 9 css rule  2 scss
    config.module.rules[9].use[2].options = {
      prependData: '@import "~@/styles/element-variables.scss";',
      sassOptions: {
        outputStyle: 'expanded'
      }
    }
    config.devServer = {
      proxy: {
        '/dev-api': {
          target: 'https://zjyth.youmatech.com/',
          changeOrigin: true,
          pathRewrite: {
            ['^' + '/dev-api']: ''
          }
        },
        '/api': {
          target: 'https://zjyth.youmatech.com/',
          changeOrigin: true
        }
      }
    }
    return config
    // config.module.rules.push({
    //   test: /\.jsx?$/,
    //   exclude: /node_modules/,
    //   use:[
    //     {
    //       loader:'webpack-strip-block',
    //       options:{
    //         start: 'develblock:start',
    //         end: 'develblock:end'
    //       }
    //     },
    //     {
    //       loader:'babel-loader'
    //     }
    //   ]
    // })
  }
};
export default config;

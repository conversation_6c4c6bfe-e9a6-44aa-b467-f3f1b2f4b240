import { INITIAL_VIEWPORTS } from '@storybook/addon-viewport';
import Vue from 'vue'
import 'normalize.css/normalize.css' // a modern alternative to CSS resets
import Element from 'element-zjui'
import moment from 'moment'
import '../public/assets/styles/element-zjui/lib/index.css'
import '@/styles/index.scss'
import components from '@/components'
import * as filters from '@/filters' // global filters

Vue.prototype.$moment = moment // 批量注册公共组件
Vue.use(components)

Vue.use(Element, {
  size: 'small'
})
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})


Vue.prototype.$load = () => {
  return Vue.prototype.$loading({
    lock: true,
    customClass: 'customLoading',
    background: 'rgba(0, 0, 0, 0.5)'
  })
}




/** @type { import('@storybook/vue').Preview } */
const preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    viewport: {
      viewports: INITIAL_VIEWPORTS,
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },
};

export default preview;

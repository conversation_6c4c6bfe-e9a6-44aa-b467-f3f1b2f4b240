pipeline {
  agent {
    node {
      label 'nodejs18'
    }

  }
  stages {
    stage('git checkout') {
      steps {
        git(url: 'https://git.cscec.com/cspm/frontend/pc.git', credentialsId: 'fanchaoyang', branch: 'main', changelog: true, poll: false)
      }
    }

    stage('edit config') {
      steps {
        sh '''rm package-lock.json
rm .npmrc'''
        sh 'echo registry = https://maven.cscec.com/repository/npm_cscec_cspm_repo/ >> .npmrc'
      }
    }

    stage('npm run build') {
      steps {
        container('nodejs18') {
          echo 'nodejs18 单元测试'
          sh '''node -v && npm -v
# npm cache clean --force
npm install
npm run build'''
        }

      }
    }

    stage('查看目录') {
      steps {
        sh 'ls'
      }
    }

    stage('image build') {
      steps {
        container('nodejs18') {
          withCredentials([usernamePassword(credentialsId : 'docker' ,passwordVariable : 'DOCKER_PASSWORD' ,usernameVariable : 'DOCKER_USERNAME' ,)]) {
            sh 'echo "$DOCKER_PASSWORD" | docker login $REGISTRY -u "$DOCKER_USERNAME" --password-stdin'
            sh 'docker build -t harbor.cscec.com/cscec-cspm/cspm-pc:latest .'
          }

        }

      }
    }

    stage('image push') {
      steps {
        container('nodejs18') {
          sh 'docker push  harbor.cscec.com/cscec-cspm/cspm-pc:latest'
        }

      }
    }

  }
}

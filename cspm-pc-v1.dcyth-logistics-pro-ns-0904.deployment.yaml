kind: Deployment
apiVersion: apps/v1
metadata:
  name: cspm-pc-v1
  namespace: dcyth-logistics-pro-ns-0904
  labels:
    app: cspm-pc
    version: v1
  annotations:
    cloudbases.io/creator: dcyth-logistics-prod-user
    deployment.kubernetes.io/revision: '1'
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cspm-pc
      version: v1
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: cspm-pc
        version: v1
      annotations:
        cloudbases.io/containerSecrets: '{"container-roo28n":"docker"}'
    spec:
      containers:
        - name: container-roo28n
          image: '$REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$APP_ENV-$BUILD_NUMBER'
          ports:
            - name: http-80
              containerPort: 80
              protocol: TCP
          resources:
            limits:
              cpu: '1'
              memory: 1000Mi
            requests:
              cpu: '1'
              memory: 1000Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      imagePullSecrets:
        - name: docker
      affinity: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600

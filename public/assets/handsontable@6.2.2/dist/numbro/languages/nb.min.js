!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).nb=e()}}(function(){return function e(n,r,o){function i(f,u){if(!r[f]){if(!n[f]){var l="function"==typeof require&&require;if(!u&&l)return l(f,!0);if(t)return t(f,!0);var d=new Error("Cannot find module '"+f+"'");throw d.code="MODULE_NOT_FOUND",d}var a=r[f]={exports:{}};n[f][0].call(a.exports,function(e){var r=n[f][1][e];return i(r||e)},a,a.exports,e,n,r,o)}return r[f].exports}for(var t="function"==typeof require&&require,f=0;f<o.length;f++)i(o[f]);return i}({1:[function(e,n,r){"use strict";n.exports={languageTag:"nb",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"t",million:"mil",billion:"mia",trillion:"b"},ordinal:function(){return"."},currency:{symbol:"kr",code:"NOK"}}},{}]},{},[1])(1)});
//# sourceMappingURL=nb.min.js.map

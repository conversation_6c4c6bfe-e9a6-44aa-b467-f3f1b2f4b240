!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).id=e()}}(function(){return function e(n,r,o){function i(f,u){if(!r[f]){if(!n[f]){var d="function"==typeof require&&require;if(!u&&d)return d(f,!0);if(t)return t(f,!0);var l=new Error("Cannot find module '"+f+"'");throw l.code="MODULE_NOT_FOUND",l}var a=r[f]={exports:{}};n[f][0].call(a.exports,function(e){var r=n[f][1][e];return i(r||e)},a,a.exports,e,n,r,o)}return r[f].exports}for(var t="function"==typeof require&&require,f=0;f<o.length;f++)i(o[f]);return i}({1:[function(e,n,r){"use strict";n.exports={languageTag:"id",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"r",million:"j",billion:"m",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"Rp",code:"IDR"}}},{}]},{},[1])(1)});
//# sourceMappingURL=id.min.js.map

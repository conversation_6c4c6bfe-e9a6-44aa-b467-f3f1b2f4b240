!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).deAT=e()}}(function(){return function e(n,o,r){function t(f,u){if(!o[f]){if(!n[f]){var d="function"==typeof require&&require;if(!u&&d)return d(f,!0);if(i)return i(f,!0);var l=new Error("Cannot find module '"+f+"'");throw l.code="MODULE_NOT_FOUND",l}var a=o[f]={exports:{}};n[f][0].call(a.exports,function(e){var o=n[f][1][e];return t(o||e)},a,a.exports,e,n,o,r)}return o[f].exports}for(var i="function"==typeof require&&require,f=0;f<r.length;f++)t(r[f]);return t}({1:[function(e,n,o){"use strict";n.exports={languageTag:"de-AT",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)});
//# sourceMappingURL=de-AT.min.js.map

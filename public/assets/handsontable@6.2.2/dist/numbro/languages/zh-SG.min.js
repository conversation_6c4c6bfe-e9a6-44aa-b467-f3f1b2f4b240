!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).zhSG=e()}}(function(){return function e(n,o,r){function i(f,u){if(!o[f]){if(!n[f]){var d="function"==typeof require&&require;if(!u&&d)return d(f,!0);if(t)return t(f,!0);var l=new Error("Cannot find module '"+f+"'");throw l.code="MODULE_NOT_FOUND",l}var a=o[f]={exports:{}};n[f][0].call(a.exports,function(e){var o=n[f][1][e];return i(o||e)},a,a.exports,e,n,o,r)}return o[f].exports}for(var t="function"==typeof require&&require,f=0;f<r.length;f++)i(r[f]);return i}({1:[function(e,n,o){"use strict";n.exports={languageTag:"zh-SG",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十亿",trillion:"兆"},ordinal:function(){return"."},currency:{symbol:"$",code:"SGD"}}},{}]},{},[1])(1)});
//# sourceMappingURL=zh-SG.min.js.map

!function(E,T){if("object"==typeof exports&&"object"==typeof module)module.exports=T(require("../../handsontable"));else if("function"==typeof define&&define.amd)define(["../../handsontable"],T);else{var _=T("object"==typeof exports?require("../../handsontable"):E.Handsontable);for(var N in _)("object"==typeof exports?exports:E)[N]=_[N]}}("undefined"!=typeof self?self:this,function(E){return function(E){function T(N){if(_[N])return _[N].exports;var O=_[N]={i:N,l:!1,exports:{}};return E[N].call(O.exports,O,O.exports,T),O.l=!0,O.exports}var _={};return T.m=E,T.c=_,T.d=function(E,_,N){T.o(E,_)||Object.defineProperty(E,_,{configurable:!1,enumerable:!0,get:N})},T.n=function(E){var _=E&&E.__esModule?function(){return E.default}:function(){return E};return T.d(_,"a",_),_},T.o=function(E,T){return Object.prototype.hasOwnProperty.call(E,T)},T.p="",T(T.s=12)}({0:function(T,_){T.exports=E},12:function(E,T,_){"use strict";function N(E,T,_){return T in E?Object.defineProperty(E,T,{value:_,enumerable:!0,configurable:!0,writable:!0}):E[T]=_,E}T.__esModule=!0,T.default=void 0;var O,e=function(E){return E&&E.__esModule?E:{default:E}}(_(0)),I=e.default.languages.dictionaryKeys,S=(O={languageCode:"pl-PL"},N(O,I.CONTEXTMENU_ITEMS_ROW_ABOVE,"Wstaw wiersz powyżej"),N(O,I.CONTEXTMENU_ITEMS_ROW_BELOW,"Wstaw wiersz poniżej"),N(O,I.CONTEXTMENU_ITEMS_INSERT_LEFT,"Wstaw kolumnę z lewej"),N(O,I.CONTEXTMENU_ITEMS_INSERT_RIGHT,"Wstaw kolumnę z prawej"),N(O,I.CONTEXTMENU_ITEMS_REMOVE_ROW,["Usuń wiersz","Usuń wiersze"]),N(O,I.CONTEXTMENU_ITEMS_REMOVE_COLUMN,["Usuń kolumnę","Usuń kolumny"]),N(O,I.CONTEXTMENU_ITEMS_UNDO,"Cofnij"),N(O,I.CONTEXTMENU_ITEMS_REDO,"Ponów"),N(O,I.CONTEXTMENU_ITEMS_READ_ONLY,"Tylko do odczytu"),N(O,I.CONTEXTMENU_ITEMS_CLEAR_COLUMN,"Wyczyść kolumnę"),N(O,I.CONTEXTMENU_ITEMS_ALIGNMENT,"Wyrównanie"),N(O,I.CONTEXTMENU_ITEMS_ALIGNMENT_LEFT,"Do lewej"),N(O,I.CONTEXTMENU_ITEMS_ALIGNMENT_CENTER,"Do środka"),N(O,I.CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT,"Do prawej"),N(O,I.CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY,"Wyjustuj"),N(O,I.CONTEXTMENU_ITEMS_ALIGNMENT_TOP,"Do góry"),N(O,I.CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE,"Wyśrodkuj"),N(O,I.CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM,"Do dołu"),N(O,I.CONTEXTMENU_ITEMS_FREEZE_COLUMN,"Zablokuj kolumnę"),N(O,I.CONTEXTMENU_ITEMS_UNFREEZE_COLUMN,"Odblokuj kolumnę"),N(O,I.CONTEXTMENU_ITEMS_BORDERS,"Obramowanie"),N(O,I.CONTEXTMENU_ITEMS_BORDERS_TOP,"Krawędź górna"),N(O,I.CONTEXTMENU_ITEMS_BORDERS_RIGHT,"Krawędź prawa"),N(O,I.CONTEXTMENU_ITEMS_BORDERS_BOTTOM,"Krawędź dolna"),N(O,I.CONTEXTMENU_ITEMS_BORDERS_LEFT,"Krawędź lewa"),N(O,I.CONTEXTMENU_ITEMS_REMOVE_BORDERS,"Usuń obramowanie(a)"),N(O,I.CONTEXTMENU_ITEMS_ADD_COMMENT,"Dodaj komentarz"),N(O,I.CONTEXTMENU_ITEMS_EDIT_COMMENT,"Edytuj komentarz"),N(O,I.CONTEXTMENU_ITEMS_REMOVE_COMMENT,"Usuń komentarz"),N(O,I.CONTEXTMENU_ITEMS_READ_ONLY_COMMENT,"Komentarz tylko do odczytu"),N(O,I.CONTEXTMENU_ITEMS_MERGE_CELLS,"Scal komórki"),N(O,I.CONTEXTMENU_ITEMS_UNMERGE_CELLS,"Rozdziel komórki"),N(O,I.CONTEXTMENU_ITEMS_COPY,"Kopiuj"),N(O,I.CONTEXTMENU_ITEMS_CUT,"Wytnij"),N(O,I.CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD,"Wstaw wiersz podrzędny"),N(O,I.CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD,"Odłącz od nadrzędnego"),N(O,I.CONTEXTMENU_ITEMS_HIDE_COLUMN,["Ukryj kolumnę","Ukryj kolumny"]),N(O,I.CONTEXTMENU_ITEMS_SHOW_COLUMN,["Pokaż kolumnę","Pokaż kolumny"]),N(O,I.CONTEXTMENU_ITEMS_HIDE_ROW,["Ukryj wiersz","Ukryj wiersze"]),N(O,I.CONTEXTMENU_ITEMS_SHOW_ROW,["Pokaż wiersz","Pokaż wiersze"]),N(O,I.FILTERS_CONDITIONS_NONE,"Brak"),N(O,I.FILTERS_CONDITIONS_EMPTY,"Komórka jest pusta"),N(O,I.FILTERS_CONDITIONS_NOT_EMPTY,"Komórka nie jest pusta"),N(O,I.FILTERS_CONDITIONS_EQUAL,"Jest równe"),N(O,I.FILTERS_CONDITIONS_NOT_EQUAL,"Jest różne od"),N(O,I.FILTERS_CONDITIONS_BEGINS_WITH,"Tekst zaczyna się od"),N(O,I.FILTERS_CONDITIONS_ENDS_WITH,"Tekst kończy się na"),N(O,I.FILTERS_CONDITIONS_CONTAINS,"Tekst zawiera fragment"),N(O,I.FILTERS_CONDITIONS_NOT_CONTAIN,"Tekst nie zawiera fragmentu"),N(O,I.FILTERS_CONDITIONS_GREATER_THAN,"Większe niż"),N(O,I.FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL,"Większe lub równe"),N(O,I.FILTERS_CONDITIONS_LESS_THAN,"Mniejsze niż"),N(O,I.FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL,"Mniejsze lub równe"),N(O,I.FILTERS_CONDITIONS_BETWEEN,"Jest pomiędzy"),N(O,I.FILTERS_CONDITIONS_NOT_BETWEEN,"Nie jest pomiędzy"),N(O,I.FILTERS_CONDITIONS_AFTER,"Data późniejsza niż"),N(O,I.FILTERS_CONDITIONS_BEFORE,"Data wcześniejsza niż"),N(O,I.FILTERS_CONDITIONS_TODAY,"Dzisiaj"),N(O,I.FILTERS_CONDITIONS_TOMORROW,"Jutro"),N(O,I.FILTERS_CONDITIONS_YESTERDAY,"Wczoraj"),N(O,I.FILTERS_VALUES_BLANK_CELLS,"Puste miejsca"),N(O,I.FILTERS_DIVS_FILTER_BY_CONDITION,"Filtruj wg warunku"),N(O,I.FILTERS_DIVS_FILTER_BY_VALUE,"Filtruj wg wartości"),N(O,I.FILTERS_LABELS_CONJUNCTION,"Oraz"),N(O,I.FILTERS_LABELS_DISJUNCTION,"Lub"),N(O,I.FILTERS_BUTTONS_SELECT_ALL,"Zaznacz wszystko"),N(O,I.FILTERS_BUTTONS_CLEAR,"Wyczyść"),N(O,I.FILTERS_BUTTONS_OK,"OK"),N(O,I.FILTERS_BUTTONS_CANCEL,"Anuluj"),N(O,I.FILTERS_BUTTONS_PLACEHOLDER_SEARCH,"Szukaj"),N(O,I.FILTERS_BUTTONS_PLACEHOLDER_VALUE,"Wartość"),N(O,I.FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE,"Druga wartość"),O);e.default.languages.registerLanguageDictionary(S),T.default=S}}).___});
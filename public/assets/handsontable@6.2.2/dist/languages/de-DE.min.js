!function(E,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("../../handsontable"));else if("function"==typeof define&&define.amd)define(["../../handsontable"],e);else{var T=e("object"==typeof exports?require("../../handsontable"):E.Handsontable);for(var _ in T)("object"==typeof exports?exports:E)[_]=T[_]}}("undefined"!=typeof self?self:this,function(E){return function(E){function e(_){if(T[_])return T[_].exports;var N=T[_]={i:_,l:!1,exports:{}};return E[_].call(N.exports,N,N.exports,e),N.l=!0,N.exports}var T={};return e.m=E,e.c=T,e.d=function(E,T,_){e.o(E,T)||Object.defineProperty(E,T,{configurable:!1,enumerable:!0,get:_})},e.n=function(E){var T=E&&E.__esModule?function(){return E.default}:function(){return E};return e.d(T,"a",T),T},e.o=function(E,e){return Object.prototype.hasOwnProperty.call(E,e)},e.p="",e(e.s=2)}([function(e,T){e.exports=E},,function(E,e,T){"use strict";function _(E,e,T){return e in E?Object.defineProperty(E,e,{value:T,enumerable:!0,configurable:!0,writable:!0}):E[e]=T,E}e.__esModule=!0,e.default=void 0;var N,n=function(E){return E&&E.__esModule?E:{default:E}}(T(0)),O=n.default.languages.dictionaryKeys,I=(N={languageCode:"de-DE"},_(N,O.CONTEXTMENU_ITEMS_ROW_ABOVE,"Zeile einfügen oberhalb"),_(N,O.CONTEXTMENU_ITEMS_ROW_BELOW,"Zeile einfügen unterhalb"),_(N,O.CONTEXTMENU_ITEMS_INSERT_LEFT,"Spalte einfügen links"),_(N,O.CONTEXTMENU_ITEMS_INSERT_RIGHT,"Spalte einfügen rechts"),_(N,O.CONTEXTMENU_ITEMS_REMOVE_ROW,["Zeile löschen","Zeilen löschen"]),_(N,O.CONTEXTMENU_ITEMS_REMOVE_COLUMN,["Spalte löschen","Spalten löschen"]),_(N,O.CONTEXTMENU_ITEMS_UNDO,"Rückgangig"),_(N,O.CONTEXTMENU_ITEMS_REDO,"Wiederholen"),_(N,O.CONTEXTMENU_ITEMS_READ_ONLY,"Nur Lesezugriff"),_(N,O.CONTEXTMENU_ITEMS_CLEAR_COLUMN,"Spalteninhalt löschen"),_(N,O.CONTEXTMENU_ITEMS_ALIGNMENT,"Ausrichtung"),_(N,O.CONTEXTMENU_ITEMS_ALIGNMENT_LEFT,"Linksbündig"),_(N,O.CONTEXTMENU_ITEMS_ALIGNMENT_CENTER,"Zentriert"),_(N,O.CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT,"Rechtsbündig"),_(N,O.CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY,"Blocksatz"),_(N,O.CONTEXTMENU_ITEMS_ALIGNMENT_TOP,"Oben"),_(N,O.CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE,"Mitte"),_(N,O.CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM,"Unten"),_(N,O.CONTEXTMENU_ITEMS_FREEZE_COLUMN,"Spalte fixieren"),_(N,O.CONTEXTMENU_ITEMS_UNFREEZE_COLUMN,"Spaltenfixierung aufheben"),_(N,O.CONTEXTMENU_ITEMS_BORDERS,"Rahmen"),_(N,O.CONTEXTMENU_ITEMS_BORDERS_TOP,"Oben"),_(N,O.CONTEXTMENU_ITEMS_BORDERS_RIGHT,"Rechts"),_(N,O.CONTEXTMENU_ITEMS_BORDERS_BOTTOM,"Unten"),_(N,O.CONTEXTMENU_ITEMS_BORDERS_LEFT,"Links"),_(N,O.CONTEXTMENU_ITEMS_REMOVE_BORDERS,"Kein Rahmen"),_(N,O.CONTEXTMENU_ITEMS_ADD_COMMENT,"Kommentar hinzufügen"),_(N,O.CONTEXTMENU_ITEMS_EDIT_COMMENT,"Kommentar bearbeiten"),_(N,O.CONTEXTMENU_ITEMS_REMOVE_COMMENT,"Kommentar löschen"),_(N,O.CONTEXTMENU_ITEMS_READ_ONLY_COMMENT,"Schreibschutz Kommentar"),_(N,O.CONTEXTMENU_ITEMS_MERGE_CELLS,"Zellen verbinden"),_(N,O.CONTEXTMENU_ITEMS_UNMERGE_CELLS,"Zellen teilen"),_(N,O.CONTEXTMENU_ITEMS_COPY,"Kopieren"),_(N,O.CONTEXTMENU_ITEMS_CUT,"Ausschneiden"),_(N,O.CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD,"Nachfolgerzeile einfügen"),_(N,O.CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD,"Von Vorgängerzeile abkoppeln"),_(N,O.CONTEXTMENU_ITEMS_HIDE_COLUMN,["Spalte ausblenden","Spalten ausblenden"]),_(N,O.CONTEXTMENU_ITEMS_SHOW_COLUMN,["Spalte einblenden","Spalten einblenden"]),_(N,O.CONTEXTMENU_ITEMS_HIDE_ROW,["Zeile ausblenden","Zeilen ausblenden"]),_(N,O.CONTEXTMENU_ITEMS_SHOW_ROW,["Zeile einblenden","Zeilen einblenden"]),_(N,O.FILTERS_CONDITIONS_NONE,"Kein Filter"),_(N,O.FILTERS_CONDITIONS_EMPTY,"Ist leer"),_(N,O.FILTERS_CONDITIONS_NOT_EMPTY,"Ist nicht leer"),_(N,O.FILTERS_CONDITIONS_EQUAL,"Ist gleich"),_(N,O.FILTERS_CONDITIONS_NOT_EQUAL,"Ist ungleich"),_(N,O.FILTERS_CONDITIONS_BEGINS_WITH,"Beginnt mit"),_(N,O.FILTERS_CONDITIONS_ENDS_WITH,"Endet mit"),_(N,O.FILTERS_CONDITIONS_CONTAINS,"Enthält"),_(N,O.FILTERS_CONDITIONS_NOT_CONTAIN,"Enthält nicht"),_(N,O.FILTERS_CONDITIONS_GREATER_THAN,"Größer als"),_(N,O.FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL,"Größer gleich"),_(N,O.FILTERS_CONDITIONS_LESS_THAN,"Kleiner als"),_(N,O.FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL,"Kleiner gleich"),_(N,O.FILTERS_CONDITIONS_BETWEEN,"Zwischen"),_(N,O.FILTERS_CONDITIONS_NOT_BETWEEN,"Außerhalb"),_(N,O.FILTERS_CONDITIONS_AFTER,"Nach"),_(N,O.FILTERS_CONDITIONS_BEFORE,"Vor"),_(N,O.FILTERS_CONDITIONS_TODAY,"Heute"),_(N,O.FILTERS_CONDITIONS_TOMORROW,"Morgen"),_(N,O.FILTERS_CONDITIONS_YESTERDAY,"Gestern"),_(N,O.FILTERS_VALUES_BLANK_CELLS,"Leere Zellen"),_(N,O.FILTERS_DIVS_FILTER_BY_CONDITION,"Per Bedingung filtern"),_(N,O.FILTERS_DIVS_FILTER_BY_VALUE,"Nach Zahlen filtern"),_(N,O.FILTERS_LABELS_CONJUNCTION,"Und"),_(N,O.FILTERS_LABELS_DISJUNCTION,"Oder"),_(N,O.FILTERS_BUTTONS_SELECT_ALL,"Alles auswählen"),_(N,O.FILTERS_BUTTONS_CLEAR,"Auswahl aufheben"),_(N,O.FILTERS_BUTTONS_OK,"OK"),_(N,O.FILTERS_BUTTONS_CANCEL,"Abbrechen"),_(N,O.FILTERS_BUTTONS_PLACEHOLDER_SEARCH,"Suchen"),_(N,O.FILTERS_BUTTONS_PLACEHOLDER_VALUE,"Wert"),_(N,O.FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE,"Alternativwert"),N);n.default.languages.registerLanguageDictionary(I),e.default=I}]).___});
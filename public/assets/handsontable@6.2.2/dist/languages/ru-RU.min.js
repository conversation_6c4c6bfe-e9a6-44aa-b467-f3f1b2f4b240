!function(E,T){if("object"==typeof exports&&"object"==typeof module)module.exports=T(require("../../handsontable"));else if("function"==typeof define&&define.amd)define(["../../handsontable"],T);else{var _=T("object"==typeof exports?require("../../handsontable"):E.Handsontable);for(var N in _)("object"==typeof exports?exports:E)[N]=_[N]}}("undefined"!=typeof self?self:this,function(E){return function(E){function T(N){if(_[N])return _[N].exports;var O=_[N]={i:N,l:!1,exports:{}};return E[N].call(O.exports,O,O.exports,T),O.l=!0,O.exports}var _={};return T.m=E,T.c=_,T.d=function(E,_,N){T.o(E,_)||Object.defineProperty(E,_,{configurable:!1,enumerable:!0,get:N})},T.n=function(E){var _=E&&E.__esModule?function(){return E.default}:function(){return E};return T.d(_,"a",_),_},T.o=function(E,T){return Object.prototype.hasOwnProperty.call(E,T)},T.p="",T(T.s=14)}({0:function(T,_){T.exports=E},14:function(E,T,_){"use strict";function N(E,T,_){return T in E?Object.defineProperty(E,T,{value:_,enumerable:!0,configurable:!0,writable:!0}):E[T]=_,E}T.__esModule=!0,T.default=void 0;var O,I=function(E){return E&&E.__esModule?E:{default:E}}(_(0)),S=I.default.languages.dictionaryKeys,M=(O={languageCode:"ru-RU"},N(O,S.CONTEXTMENU_ITEMS_ROW_ABOVE,"Вставить строку выше"),N(O,S.CONTEXTMENU_ITEMS_ROW_BELOW,"Вставить строку ниже"),N(O,S.CONTEXTMENU_ITEMS_INSERT_LEFT,"Вставить столбец слева"),N(O,S.CONTEXTMENU_ITEMS_INSERT_RIGHT,"Вставить столбец справа"),N(O,S.CONTEXTMENU_ITEMS_REMOVE_ROW,["Удалить строку","Удалить строки"]),N(O,S.CONTEXTMENU_ITEMS_REMOVE_COLUMN,["Удалить столбец","Удалить столбцы"]),N(O,S.CONTEXTMENU_ITEMS_UNDO,"Отменить"),N(O,S.CONTEXTMENU_ITEMS_REDO,"Повторить"),N(O,S.CONTEXTMENU_ITEMS_READ_ONLY,"Только для чтения"),N(O,S.CONTEXTMENU_ITEMS_CLEAR_COLUMN,"Очистить столбец"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT,"Выравнивание"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_LEFT,"По левому краю"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_CENTER,"По центру"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT,"По правому краю"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY,"По ширине"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_TOP,"По верхнему краю"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE,"По центру"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM,"По нижнему краю"),N(O,S.CONTEXTMENU_ITEMS_FREEZE_COLUMN,"Закрепить столбец"),N(O,S.CONTEXTMENU_ITEMS_UNFREEZE_COLUMN,"Открепить столбец"),N(O,S.CONTEXTMENU_ITEMS_BORDERS,"Границы"),N(O,S.CONTEXTMENU_ITEMS_BORDERS_TOP,"Сверху"),N(O,S.CONTEXTMENU_ITEMS_BORDERS_RIGHT,"Справа"),N(O,S.CONTEXTMENU_ITEMS_BORDERS_BOTTOM,"Снизу"),N(O,S.CONTEXTMENU_ITEMS_BORDERS_LEFT,"Слева"),N(O,S.CONTEXTMENU_ITEMS_REMOVE_BORDERS,"Удалить границу(ы)"),N(O,S.CONTEXTMENU_ITEMS_ADD_COMMENT,"Добавить комментарий"),N(O,S.CONTEXTMENU_ITEMS_EDIT_COMMENT,"Редактировать комментарий"),N(O,S.CONTEXTMENU_ITEMS_REMOVE_COMMENT,"Удалить комментарий"),N(O,S.CONTEXTMENU_ITEMS_READ_ONLY_COMMENT,"Комментарий только для чтения"),N(O,S.CONTEXTMENU_ITEMS_MERGE_CELLS,"Объединить ячейки"),N(O,S.CONTEXTMENU_ITEMS_UNMERGE_CELLS,"Разделить ячейки"),N(O,S.CONTEXTMENU_ITEMS_COPY,"Копировать"),N(O,S.CONTEXTMENU_ITEMS_CUT,"Вырезать"),N(O,S.CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD,"Вставить дочернюю строку"),N(O,S.CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD,"Отделить от родителя"),N(O,S.CONTEXTMENU_ITEMS_HIDE_COLUMN,["Скрыть столбец","Скрыть столбцы"]),N(O,S.CONTEXTMENU_ITEMS_SHOW_COLUMN,["Показать столбец","Показать столбцы"]),N(O,S.CONTEXTMENU_ITEMS_HIDE_ROW,["Скрыть строку","Скрыть строки"]),N(O,S.CONTEXTMENU_ITEMS_SHOW_ROW,["Показать строку","Показать строки"]),N(O,S.FILTERS_CONDITIONS_NONE,"Отсутствует"),N(O,S.FILTERS_CONDITIONS_EMPTY,"Пусто"),N(O,S.FILTERS_CONDITIONS_NOT_EMPTY,"Не пусто"),N(O,S.FILTERS_CONDITIONS_EQUAL,"Равно"),N(O,S.FILTERS_CONDITIONS_NOT_EQUAL,"Не равно"),N(O,S.FILTERS_CONDITIONS_BEGINS_WITH,"Начинается на"),N(O,S.FILTERS_CONDITIONS_ENDS_WITH,"Заканчивается на"),N(O,S.FILTERS_CONDITIONS_CONTAINS,"Содержит"),N(O,S.FILTERS_CONDITIONS_NOT_CONTAIN,"Не содержит"),N(O,S.FILTERS_CONDITIONS_GREATER_THAN,"Больше чем"),N(O,S.FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL,"Больше или равно"),N(O,S.FILTERS_CONDITIONS_LESS_THAN,"Меньше чем"),N(O,S.FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL,"Меньше или равно"),N(O,S.FILTERS_CONDITIONS_BETWEEN,"Между"),N(O,S.FILTERS_CONDITIONS_NOT_BETWEEN,"Не между"),N(O,S.FILTERS_CONDITIONS_AFTER,"После"),N(O,S.FILTERS_CONDITIONS_BEFORE,"До"),N(O,S.FILTERS_CONDITIONS_TODAY,"Сегодня"),N(O,S.FILTERS_CONDITIONS_TOMORROW,"Завтра"),N(O,S.FILTERS_CONDITIONS_YESTERDAY,"Вчера"),N(O,S.FILTERS_VALUES_BLANK_CELLS,"Пустые ячейки"),N(O,S.FILTERS_DIVS_FILTER_BY_CONDITION,"Фильтр по условию"),N(O,S.FILTERS_DIVS_FILTER_BY_VALUE,"Фильтр по значению"),N(O,S.FILTERS_LABELS_CONJUNCTION,"И"),N(O,S.FILTERS_LABELS_DISJUNCTION,"Или"),N(O,S.FILTERS_BUTTONS_SELECT_ALL,"Выбрать все"),N(O,S.FILTERS_BUTTONS_CLEAR,"Убрать"),N(O,S.FILTERS_BUTTONS_OK,"OK"),N(O,S.FILTERS_BUTTONS_CANCEL,"Отмена"),N(O,S.FILTERS_BUTTONS_PLACEHOLDER_SEARCH,"Поиск"),N(O,S.FILTERS_BUTTONS_PLACEHOLDER_VALUE,"Значение"),N(O,S.FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE,"Второе значение"),O);I.default.languages.registerLanguageDictionary(M),T.default=M}}).___});
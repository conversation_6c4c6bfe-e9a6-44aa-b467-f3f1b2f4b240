!function(E,T){if("object"==typeof exports&&"object"==typeof module)module.exports=T(require("../../handsontable"));else if("function"==typeof define&&define.amd)define(["../../handsontable"],T);else{var _=T("object"==typeof exports?require("../../handsontable"):E.Handsontable);for(var N in _)("object"==typeof exports?exports:E)[N]=_[N]}}("undefined"!=typeof self?self:this,function(E){return function(E){function T(N){if(_[N])return _[N].exports;var e=_[N]={i:N,l:!1,exports:{}};return E[N].call(e.exports,e,e.exports,T),e.l=!0,e.exports}var _={};return T.m=E,T.c=_,T.d=function(E,_,N){T.o(E,_)||Object.defineProperty(E,_,{configurable:!1,enumerable:!0,get:N})},T.n=function(E){var _=E&&E.__esModule?function(){return E.default}:function(){return E};return T.d(_,"a",_),_},T.o=function(E,T){return Object.prototype.hasOwnProperty.call(E,T)},T.p="",T(T.s=4)}([function(T,_){T.exports=E},,,,function(E,T,_){"use strict";function N(E,T,_){return T in E?Object.defineProperty(E,T,{value:_,enumerable:!0,configurable:!0,writable:!0}):E[T]=_,E}T.__esModule=!0,T.default=void 0;var e,O=function(E){return E&&E.__esModule?E:{default:E}}(_(0)),r=O.default.languages.dictionaryKeys,I=(e={languageCode:"es-MX"},N(e,r.CONTEXTMENU_ITEMS_ROW_ABOVE,"Insertar fila arriba"),N(e,r.CONTEXTMENU_ITEMS_ROW_BELOW,"Insertar fila abajo"),N(e,r.CONTEXTMENU_ITEMS_INSERT_LEFT,"Insertar columna izquierda"),N(e,r.CONTEXTMENU_ITEMS_INSERT_RIGHT,"Insertar columna derecha"),N(e,r.CONTEXTMENU_ITEMS_REMOVE_ROW,["Eliminar fila","Eliminar filas"]),N(e,r.CONTEXTMENU_ITEMS_REMOVE_COLUMN,["Eliminar columna","Eliminar columnas"]),N(e,r.CONTEXTMENU_ITEMS_UNDO,"Deshacer"),N(e,r.CONTEXTMENU_ITEMS_REDO,"Rehacer"),N(e,r.CONTEXTMENU_ITEMS_READ_ONLY,"Solo lectura"),N(e,r.CONTEXTMENU_ITEMS_CLEAR_COLUMN,"Limpiar columna"),N(e,r.CONTEXTMENU_ITEMS_ALIGNMENT,"Alineación"),N(e,r.CONTEXTMENU_ITEMS_ALIGNMENT_LEFT,"Izquierda"),N(e,r.CONTEXTMENU_ITEMS_ALIGNMENT_CENTER,"Centro"),N(e,r.CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT,"Derecha"),N(e,r.CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY,"Justificar"),N(e,r.CONTEXTMENU_ITEMS_ALIGNMENT_TOP,"Superior"),N(e,r.CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE,"Medio"),N(e,r.CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM,"Inferior"),N(e,r.CONTEXTMENU_ITEMS_FREEZE_COLUMN,"Congelar columna"),N(e,r.CONTEXTMENU_ITEMS_UNFREEZE_COLUMN,"Descongelar columna"),N(e,r.CONTEXTMENU_ITEMS_BORDERS,"Bordes"),N(e,r.CONTEXTMENU_ITEMS_BORDERS_TOP,"Superior"),N(e,r.CONTEXTMENU_ITEMS_BORDERS_RIGHT,"Derecho"),N(e,r.CONTEXTMENU_ITEMS_BORDERS_BOTTOM,"Inferior"),N(e,r.CONTEXTMENU_ITEMS_BORDERS_LEFT,"Izquierdo"),N(e,r.CONTEXTMENU_ITEMS_REMOVE_BORDERS,"Quitar borde(s)"),N(e,r.CONTEXTMENU_ITEMS_ADD_COMMENT,"Agregar comentario"),N(e,r.CONTEXTMENU_ITEMS_EDIT_COMMENT,"Editar comentario"),N(e,r.CONTEXTMENU_ITEMS_REMOVE_COMMENT,"Borrar comentario"),N(e,r.CONTEXTMENU_ITEMS_READ_ONLY_COMMENT,"Comentario Solo de lectura"),N(e,r.CONTEXTMENU_ITEMS_MERGE_CELLS,"Unir celdas"),N(e,r.CONTEXTMENU_ITEMS_UNMERGE_CELLS,"Separar celdas"),N(e,r.CONTEXTMENU_ITEMS_COPY,"Copiar"),N(e,r.CONTEXTMENU_ITEMS_CUT,"Cortar"),N(e,r.CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD,"Insertar fila hija"),N(e,r.CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD,"Separar del padre"),N(e,r.CONTEXTMENU_ITEMS_HIDE_COLUMN,["Esconder columna","Esconder columnas"]),N(e,r.CONTEXTMENU_ITEMS_SHOW_COLUMN,["Mostrar columna","Mostrar columnas"]),N(e,r.CONTEXTMENU_ITEMS_HIDE_ROW,["Esconder fila","Esconder filas"]),N(e,r.CONTEXTMENU_ITEMS_SHOW_ROW,["Mostrar fila","Mostrar filas"]),N(e,r.FILTERS_CONDITIONS_NONE,"Ninguna"),N(e,r.FILTERS_CONDITIONS_EMPTY,"Está vacío"),N(e,r.FILTERS_CONDITIONS_NOT_EMPTY,"No está vacío"),N(e,r.FILTERS_CONDITIONS_EQUAL,"Es igual a"),N(e,r.FILTERS_CONDITIONS_NOT_EQUAL,"No es igual a"),N(e,r.FILTERS_CONDITIONS_BEGINS_WITH,"Comienza con"),N(e,r.FILTERS_CONDITIONS_ENDS_WITH,"Termina con"),N(e,r.FILTERS_CONDITIONS_CONTAINS,"Contiene"),N(e,r.FILTERS_CONDITIONS_NOT_CONTAIN,"No contiene"),N(e,r.FILTERS_CONDITIONS_GREATER_THAN,"Mayor que"),N(e,r.FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL,"Mayor o igual que"),N(e,r.FILTERS_CONDITIONS_LESS_THAN,"Menor que"),N(e,r.FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL,"Menor o igual que"),N(e,r.FILTERS_CONDITIONS_BETWEEN,"Es entre"),N(e,r.FILTERS_CONDITIONS_NOT_BETWEEN,"No es entre"),N(e,r.FILTERS_CONDITIONS_AFTER,"Después"),N(e,r.FILTERS_CONDITIONS_BEFORE,"Antes"),N(e,r.FILTERS_CONDITIONS_TODAY,"Hoy"),N(e,r.FILTERS_CONDITIONS_TOMORROW,"Mañana"),N(e,r.FILTERS_CONDITIONS_YESTERDAY,"Ayer"),N(e,r.FILTERS_VALUES_BLANK_CELLS,"Celdas vacías"),N(e,r.FILTERS_DIVS_FILTER_BY_CONDITION,"Filtrar por condición"),N(e,r.FILTERS_DIVS_FILTER_BY_VALUE,"Filtrar por valor"),N(e,r.FILTERS_LABELS_CONJUNCTION,"Y"),N(e,r.FILTERS_LABELS_DISJUNCTION,"O"),N(e,r.FILTERS_BUTTONS_SELECT_ALL,"Seleccionar todo"),N(e,r.FILTERS_BUTTONS_CLEAR,"Borrar"),N(e,r.FILTERS_BUTTONS_OK,"OK"),N(e,r.FILTERS_BUTTONS_CANCEL,"Cancelar"),N(e,r.FILTERS_BUTTONS_PLACEHOLDER_SEARCH,"Buscar"),N(e,r.FILTERS_BUTTONS_PLACEHOLDER_VALUE,"Valor"),N(e,r.FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE,"Valor secundario"),e);O.default.languages.registerLanguageDictionary(I),T.default=I}]).___});
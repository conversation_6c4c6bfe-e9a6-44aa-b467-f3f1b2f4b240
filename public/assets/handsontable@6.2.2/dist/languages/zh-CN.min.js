!function(E,T){if("object"==typeof exports&&"object"==typeof module)module.exports=T(require("../../handsontable"));else if("function"==typeof define&&define.amd)define(["../../handsontable"],T);else{var _=T("object"==typeof exports?require("../../handsontable"):E.Handsontable);for(var N in _)("object"==typeof exports?exports:E)[N]=_[N]}}("undefined"!=typeof self?self:this,function(E){return function(E){function T(N){if(_[N])return _[N].exports;var O=_[N]={i:N,l:!1,exports:{}};return E[N].call(O.exports,O,O.exports,T),O.l=!0,O.exports}var _={};return T.m=E,T.c=_,T.d=function(E,_,N){T.o(E,_)||Object.defineProperty(E,_,{configurable:!1,enumerable:!0,get:N})},T.n=function(E){var _=E&&E.__esModule?function(){return E.default}:function(){return E};return T.d(_,"a",_),_},T.o=function(E,T){return Object.prototype.hasOwnProperty.call(E,T)},T.p="",T(T.s=15)}({0:function(T,_){T.exports=E},15:function(E,T,_){"use strict";function N(E,T,_){return T in E?Object.defineProperty(E,T,{value:_,enumerable:!0,configurable:!0,writable:!0}):E[T]=_,E}T.__esModule=!0,T.default=void 0;var O,I=function(E){return E&&E.__esModule?E:{default:E}}(_(0)),S=I.default.languages.dictionaryKeys,M=(O={languageCode:"zh-CN"},N(O,S.CONTEXTMENU_ITEMS_ROW_ABOVE,"上方插入行"),N(O,S.CONTEXTMENU_ITEMS_ROW_BELOW,"下方插入行"),N(O,S.CONTEXTMENU_ITEMS_INSERT_LEFT,"左方插入列"),N(O,S.CONTEXTMENU_ITEMS_INSERT_RIGHT,"右方插入列"),N(O,S.CONTEXTMENU_ITEMS_REMOVE_ROW,["移除该行","移除多行"]),N(O,S.CONTEXTMENU_ITEMS_REMOVE_COLUMN,["移除该列","移除多列"]),N(O,S.CONTEXTMENU_ITEMS_UNDO,"撤销"),N(O,S.CONTEXTMENU_ITEMS_REDO,"恢复"),N(O,S.CONTEXTMENU_ITEMS_READ_ONLY,"只读"),N(O,S.CONTEXTMENU_ITEMS_CLEAR_COLUMN,"清空该列"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT,"对齐"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_LEFT,"左对齐"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_CENTER,"水平居中"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT,"右对齐"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY,"两端对齐"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_TOP,"顶端对齐"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE,"垂直居中"),N(O,S.CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM,"底端对齐"),N(O,S.CONTEXTMENU_ITEMS_FREEZE_COLUMN,"冻结该列"),N(O,S.CONTEXTMENU_ITEMS_UNFREEZE_COLUMN,"取消冻结"),N(O,S.CONTEXTMENU_ITEMS_BORDERS,"边框"),N(O,S.CONTEXTMENU_ITEMS_BORDERS_TOP,"上"),N(O,S.CONTEXTMENU_ITEMS_BORDERS_RIGHT,"右"),N(O,S.CONTEXTMENU_ITEMS_BORDERS_BOTTOM,"下"),N(O,S.CONTEXTMENU_ITEMS_BORDERS_LEFT,"左"),N(O,S.CONTEXTMENU_ITEMS_REMOVE_BORDERS,"移除边框"),N(O,S.CONTEXTMENU_ITEMS_ADD_COMMENT,"插入批注"),N(O,S.CONTEXTMENU_ITEMS_EDIT_COMMENT,"编辑批注"),N(O,S.CONTEXTMENU_ITEMS_REMOVE_COMMENT,"删除批注"),N(O,S.CONTEXTMENU_ITEMS_READ_ONLY_COMMENT,"只读批注"),N(O,S.CONTEXTMENU_ITEMS_MERGE_CELLS,"合并"),N(O,S.CONTEXTMENU_ITEMS_UNMERGE_CELLS,"取消合并"),N(O,S.CONTEXTMENU_ITEMS_COPY,"复制"),N(O,S.CONTEXTMENU_ITEMS_CUT,"剪切"),N(O,S.CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD,"插入子行"),N(O,S.CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD,"与母行分离"),N(O,S.CONTEXTMENU_ITEMS_HIDE_COLUMN,["隐藏该列","隐藏多列"]),N(O,S.CONTEXTMENU_ITEMS_SHOW_COLUMN,["显示该列","显示多列"]),N(O,S.CONTEXTMENU_ITEMS_HIDE_ROW,["隐藏该行","隐藏多行"]),N(O,S.CONTEXTMENU_ITEMS_SHOW_ROW,["显示该行","显示多行"]),N(O,S.FILTERS_CONDITIONS_NONE,"无"),N(O,S.FILTERS_CONDITIONS_EMPTY,"为空"),N(O,S.FILTERS_CONDITIONS_NOT_EMPTY,"不为空"),N(O,S.FILTERS_CONDITIONS_EQUAL,"等于"),N(O,S.FILTERS_CONDITIONS_NOT_EQUAL,"不等于"),N(O,S.FILTERS_CONDITIONS_BEGINS_WITH,"开头是"),N(O,S.FILTERS_CONDITIONS_ENDS_WITH,"结尾是"),N(O,S.FILTERS_CONDITIONS_CONTAINS,"包含"),N(O,S.FILTERS_CONDITIONS_NOT_CONTAIN,"不包含"),N(O,S.FILTERS_CONDITIONS_GREATER_THAN,"大于"),N(O,S.FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL,"大于或等于"),N(O,S.FILTERS_CONDITIONS_LESS_THAN,"小于"),N(O,S.FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL,"小于或等于"),N(O,S.FILTERS_CONDITIONS_BETWEEN,"在此范围"),N(O,S.FILTERS_CONDITIONS_NOT_BETWEEN,"不在此范围"),N(O,S.FILTERS_CONDITIONS_AFTER,"之后"),N(O,S.FILTERS_CONDITIONS_BEFORE,"之前"),N(O,S.FILTERS_CONDITIONS_TODAY,"今天"),N(O,S.FILTERS_CONDITIONS_TOMORROW,"明天"),N(O,S.FILTERS_CONDITIONS_YESTERDAY,"昨天"),N(O,S.FILTERS_VALUES_BLANK_CELLS,"空白单元格"),N(O,S.FILTERS_DIVS_FILTER_BY_CONDITION,"按条件过滤"),N(O,S.FILTERS_DIVS_FILTER_BY_VALUE,"按值过滤"),N(O,S.FILTERS_LABELS_CONJUNCTION,"且"),N(O,S.FILTERS_LABELS_DISJUNCTION,"或"),N(O,S.FILTERS_BUTTONS_SELECT_ALL,"全选"),N(O,S.FILTERS_BUTTONS_CLEAR,"清除"),N(O,S.FILTERS_BUTTONS_OK,"确认"),N(O,S.FILTERS_BUTTONS_CANCEL,"取消"),N(O,S.FILTERS_BUTTONS_PLACEHOLDER_SEARCH,"搜索"),N(O,S.FILTERS_BUTTONS_PLACEHOLDER_VALUE,"值"),N(O,S.FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE,"第二值"),O);I.default.languages.registerLanguageDictionary(M),T.default=M}}).___});
!function(E,T){if("object"==typeof exports&&"object"==typeof module)module.exports=T(require("../../handsontable"));else if("function"==typeof define&&define.amd)define(["../../handsontable"],T);else{var _=T("object"==typeof exports?require("../../handsontable"):E.Handsontable);for(var N in _)("object"==typeof exports?exports:E)[N]=_[N]}}("undefined"!=typeof self?self:this,function(E){return function(E){function T(N){if(_[N])return _[N].exports;var e=_[N]={i:N,l:!1,exports:{}};return E[N].call(e.exports,e,e.exports,T),e.l=!0,e.exports}var _={};return T.m=E,T.c=_,T.d=function(E,_,N){T.o(E,_)||Object.defineProperty(E,_,{configurable:!1,enumerable:!0,get:N})},T.n=function(E){var _=E&&E.__esModule?function(){return E.default}:function(){return E};return T.d(_,"a",_),_},T.o=function(E,T){return Object.prototype.hasOwnProperty.call(E,T)},T.p="",T(T.s=3)}([function(T,_){T.exports=E},,,function(E,T,_){"use strict";function N(E,T,_){return T in E?Object.defineProperty(E,T,{value:_,enumerable:!0,configurable:!0,writable:!0}):E[T]=_,E}T.__esModule=!0,T.default=void 0;var e,O=function(E){return E&&E.__esModule?E:{default:E}}(_(0)),I=O.default.languages.dictionaryKeys,S=(e={languageCode:"en-US"},N(e,I.CONTEXTMENU_ITEMS_ROW_ABOVE,"Insert row above"),N(e,I.CONTEXTMENU_ITEMS_ROW_BELOW,"Insert row below"),N(e,I.CONTEXTMENU_ITEMS_INSERT_LEFT,"Insert column left"),N(e,I.CONTEXTMENU_ITEMS_INSERT_RIGHT,"Insert column right"),N(e,I.CONTEXTMENU_ITEMS_REMOVE_ROW,["Remove row","Remove rows"]),N(e,I.CONTEXTMENU_ITEMS_REMOVE_COLUMN,["Remove column","Remove columns"]),N(e,I.CONTEXTMENU_ITEMS_UNDO,"Undo"),N(e,I.CONTEXTMENU_ITEMS_REDO,"Redo"),N(e,I.CONTEXTMENU_ITEMS_READ_ONLY,"Read only"),N(e,I.CONTEXTMENU_ITEMS_CLEAR_COLUMN,"Clear column"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT,"Alignment"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_LEFT,"Left"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_CENTER,"Center"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT,"Right"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY,"Justify"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_TOP,"Top"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE,"Middle"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM,"Bottom"),N(e,I.CONTEXTMENU_ITEMS_FREEZE_COLUMN,"Freeze column"),N(e,I.CONTEXTMENU_ITEMS_UNFREEZE_COLUMN,"Unfreeze column"),N(e,I.CONTEXTMENU_ITEMS_BORDERS,"Borders"),N(e,I.CONTEXTMENU_ITEMS_BORDERS_TOP,"Top"),N(e,I.CONTEXTMENU_ITEMS_BORDERS_RIGHT,"Right"),N(e,I.CONTEXTMENU_ITEMS_BORDERS_BOTTOM,"Bottom"),N(e,I.CONTEXTMENU_ITEMS_BORDERS_LEFT,"Left"),N(e,I.CONTEXTMENU_ITEMS_REMOVE_BORDERS,"Remove border(s)"),N(e,I.CONTEXTMENU_ITEMS_ADD_COMMENT,"Add comment"),N(e,I.CONTEXTMENU_ITEMS_EDIT_COMMENT,"Edit comment"),N(e,I.CONTEXTMENU_ITEMS_REMOVE_COMMENT,"Delete comment"),N(e,I.CONTEXTMENU_ITEMS_READ_ONLY_COMMENT,"Read-only comment"),N(e,I.CONTEXTMENU_ITEMS_MERGE_CELLS,"Merge cells"),N(e,I.CONTEXTMENU_ITEMS_UNMERGE_CELLS,"Unmerge cells"),N(e,I.CONTEXTMENU_ITEMS_COPY,"Copy"),N(e,I.CONTEXTMENU_ITEMS_CUT,"Cut"),N(e,I.CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD,"Insert child row"),N(e,I.CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD,"Detach from parent"),N(e,I.CONTEXTMENU_ITEMS_HIDE_COLUMN,["Hide column","Hide columns"]),N(e,I.CONTEXTMENU_ITEMS_SHOW_COLUMN,["Show column","Show columns"]),N(e,I.CONTEXTMENU_ITEMS_HIDE_ROW,["Hide row","Hide rows"]),N(e,I.CONTEXTMENU_ITEMS_SHOW_ROW,["Show row","Show rows"]),N(e,I.FILTERS_CONDITIONS_NONE,"None"),N(e,I.FILTERS_CONDITIONS_EMPTY,"Is empty"),N(e,I.FILTERS_CONDITIONS_NOT_EMPTY,"Is not empty"),N(e,I.FILTERS_CONDITIONS_EQUAL,"Is equal to"),N(e,I.FILTERS_CONDITIONS_NOT_EQUAL,"Is not equal to"),N(e,I.FILTERS_CONDITIONS_BEGINS_WITH,"Begins with"),N(e,I.FILTERS_CONDITIONS_ENDS_WITH,"Ends with"),N(e,I.FILTERS_CONDITIONS_CONTAINS,"Contains"),N(e,I.FILTERS_CONDITIONS_NOT_CONTAIN,"Does not contain"),N(e,I.FILTERS_CONDITIONS_GREATER_THAN,"Greater than"),N(e,I.FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL,"Greater than or equal to"),N(e,I.FILTERS_CONDITIONS_LESS_THAN,"Less than"),N(e,I.FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL,"Less than or equal to"),N(e,I.FILTERS_CONDITIONS_BETWEEN,"Is between"),N(e,I.FILTERS_CONDITIONS_NOT_BETWEEN,"Is not between"),N(e,I.FILTERS_CONDITIONS_AFTER,"After"),N(e,I.FILTERS_CONDITIONS_BEFORE,"Before"),N(e,I.FILTERS_CONDITIONS_TODAY,"Today"),N(e,I.FILTERS_CONDITIONS_TOMORROW,"Tomorrow"),N(e,I.FILTERS_CONDITIONS_YESTERDAY,"Yesterday"),N(e,I.FILTERS_VALUES_BLANK_CELLS,"Blank cells"),N(e,I.FILTERS_DIVS_FILTER_BY_CONDITION,"Filter by condition"),N(e,I.FILTERS_DIVS_FILTER_BY_VALUE,"Filter by value"),N(e,I.FILTERS_LABELS_CONJUNCTION,"And"),N(e,I.FILTERS_LABELS_DISJUNCTION,"Or"),N(e,I.FILTERS_BUTTONS_SELECT_ALL,"Select all"),N(e,I.FILTERS_BUTTONS_CLEAR,"Clear"),N(e,I.FILTERS_BUTTONS_OK,"OK"),N(e,I.FILTERS_BUTTONS_CANCEL,"Cancel"),N(e,I.FILTERS_BUTTONS_PLACEHOLDER_SEARCH,"Search"),N(e,I.FILTERS_BUTTONS_PLACEHOLDER_VALUE,"Value"),N(e,I.FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE,"Second value"),e);O.default.languages.registerLanguageDictionary(S),T.default=S}]).___});
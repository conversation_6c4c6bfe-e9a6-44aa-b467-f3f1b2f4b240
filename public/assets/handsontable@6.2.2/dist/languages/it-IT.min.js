!function(E,T){if("object"==typeof exports&&"object"==typeof module)module.exports=T(require("../../handsontable"));else if("function"==typeof define&&define.amd)define(["../../handsontable"],T);else{var _=T("object"==typeof exports?require("../../handsontable"):E.Handsontable);for(var N in _)("object"==typeof exports?exports:E)[N]=_[N]}}("undefined"!=typeof self?self:this,function(E){return function(E){function T(N){if(_[N])return _[N].exports;var O=_[N]={i:N,l:!1,exports:{}};return E[N].call(O.exports,O,O.exports,T),O.l=!0,O.exports}var _={};return T.m=E,T.c=_,T.d=function(E,_,N){T.o(E,_)||Object.defineProperty(E,_,{configurable:!1,enumerable:!0,get:N})},T.n=function(E){var _=E&&E.__esModule?function(){return E.default}:function(){return E};return T.d(_,"a",_),_},T.o=function(E,T){return Object.prototype.hasOwnProperty.call(E,T)},T.p="",T(T.s=6)}({0:function(T,_){T.exports=E},6:function(E,T,_){"use strict";function N(E,T,_){return T in E?Object.defineProperty(E,T,{value:_,enumerable:!0,configurable:!0,writable:!0}):E[T]=_,E}T.__esModule=!0,T.default=void 0;var O,e=function(E){return E&&E.__esModule?E:{default:E}}(_(0)),o=e.default.languages.dictionaryKeys,I=(O={languageCode:"it-IT"},N(O,o.CONTEXTMENU_ITEMS_ROW_ABOVE,"Inserisci riga sopra"),N(O,o.CONTEXTMENU_ITEMS_ROW_BELOW,"Inserisci riga sotto"),N(O,o.CONTEXTMENU_ITEMS_INSERT_LEFT,"Inserisci colonna a sinistra"),N(O,o.CONTEXTMENU_ITEMS_INSERT_RIGHT,"Inserisci colonna a destra"),N(O,o.CONTEXTMENU_ITEMS_REMOVE_ROW,["Rimuovi riga","Rimuovi righe"]),N(O,o.CONTEXTMENU_ITEMS_REMOVE_COLUMN,["Rimuovi colonna","Rimuovi colonne"]),N(O,o.CONTEXTMENU_ITEMS_UNDO,"Annulla"),N(O,o.CONTEXTMENU_ITEMS_REDO,"Ripeti"),N(O,o.CONTEXTMENU_ITEMS_READ_ONLY,"Sola lettura"),N(O,o.CONTEXTMENU_ITEMS_CLEAR_COLUMN,"Svuota colonna"),N(O,o.CONTEXTMENU_ITEMS_ALIGNMENT,"Allineamento"),N(O,o.CONTEXTMENU_ITEMS_ALIGNMENT_LEFT,"Sinistra"),N(O,o.CONTEXTMENU_ITEMS_ALIGNMENT_CENTER,"Centro"),N(O,o.CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT,"Destra"),N(O,o.CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY,"Giustificato"),N(O,o.CONTEXTMENU_ITEMS_ALIGNMENT_TOP,"In alto"),N(O,o.CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE,"A metà"),N(O,o.CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM,"In basso"),N(O,o.CONTEXTMENU_ITEMS_FREEZE_COLUMN,"Blocca colonna"),N(O,o.CONTEXTMENU_ITEMS_UNFREEZE_COLUMN,"Sblocca colonna"),N(O,o.CONTEXTMENU_ITEMS_BORDERS,"Bordi"),N(O,o.CONTEXTMENU_ITEMS_BORDERS_TOP,"Sopra"),N(O,o.CONTEXTMENU_ITEMS_BORDERS_RIGHT,"Destra"),N(O,o.CONTEXTMENU_ITEMS_BORDERS_BOTTOM,"Sotto"),N(O,o.CONTEXTMENU_ITEMS_BORDERS_LEFT,"Sinistra"),N(O,o.CONTEXTMENU_ITEMS_REMOVE_BORDERS,"Rimuovi bordo(i)"),N(O,o.CONTEXTMENU_ITEMS_ADD_COMMENT,"Aggiungi commento"),N(O,o.CONTEXTMENU_ITEMS_EDIT_COMMENT,"Modifica commento"),N(O,o.CONTEXTMENU_ITEMS_REMOVE_COMMENT,"Rimuovi commento"),N(O,o.CONTEXTMENU_ITEMS_READ_ONLY_COMMENT,"Commento in sola lettura"),N(O,o.CONTEXTMENU_ITEMS_MERGE_CELLS,"Unisci celle"),N(O,o.CONTEXTMENU_ITEMS_UNMERGE_CELLS,"Separa celle"),N(O,o.CONTEXTMENU_ITEMS_COPY,"Copia"),N(O,o.CONTEXTMENU_ITEMS_CUT,"Taglia"),N(O,o.CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD,"Inserisci riga figlia"),N(O,o.CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD,"Scollega da riga madre"),N(O,o.CONTEXTMENU_ITEMS_HIDE_COLUMN,["Nascondi colonna","Nascondi colonne"]),N(O,o.CONTEXTMENU_ITEMS_SHOW_COLUMN,["Mostra colonna","Mostra colonne"]),N(O,o.CONTEXTMENU_ITEMS_HIDE_ROW,["Nascondi riga","Nascondi righe"]),N(O,o.CONTEXTMENU_ITEMS_SHOW_ROW,["Mostra riga","Mostra righe"]),N(O,o.FILTERS_CONDITIONS_NONE,"Nessuna"),N(O,o.FILTERS_CONDITIONS_EMPTY,"È vuoto"),N(O,o.FILTERS_CONDITIONS_NOT_EMPTY,"Non è vuoto"),N(O,o.FILTERS_CONDITIONS_EQUAL,"È uguale a"),N(O,o.FILTERS_CONDITIONS_NOT_EQUAL,"È diverso da"),N(O,o.FILTERS_CONDITIONS_BEGINS_WITH,"Inizia con"),N(O,o.FILTERS_CONDITIONS_ENDS_WITH,"Termina con"),N(O,o.FILTERS_CONDITIONS_CONTAINS,"Contiene"),N(O,o.FILTERS_CONDITIONS_NOT_CONTAIN,"Non contiene"),N(O,o.FILTERS_CONDITIONS_GREATER_THAN,"Maggiore"),N(O,o.FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL,"Maggiore o uguale"),N(O,o.FILTERS_CONDITIONS_LESS_THAN,"Minore"),N(O,o.FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL,"Minore o uguale"),N(O,o.FILTERS_CONDITIONS_BETWEEN,"È compreso tra"),N(O,o.FILTERS_CONDITIONS_NOT_BETWEEN,"Non è compreso tra"),N(O,o.FILTERS_CONDITIONS_AFTER,"Dopo"),N(O,o.FILTERS_CONDITIONS_BEFORE,"Prima"),N(O,o.FILTERS_CONDITIONS_TODAY,"Oggi"),N(O,o.FILTERS_CONDITIONS_TOMORROW,"Domani"),N(O,o.FILTERS_CONDITIONS_YESTERDAY,"Ieri"),N(O,o.FILTERS_VALUES_BLANK_CELLS,"Celle vuote"),N(O,o.FILTERS_DIVS_FILTER_BY_CONDITION,"Filtra per condizione"),N(O,o.FILTERS_DIVS_FILTER_BY_VALUE,"Filtra per valore"),N(O,o.FILTERS_LABELS_CONJUNCTION,"E"),N(O,o.FILTERS_LABELS_DISJUNCTION,"O"),N(O,o.FILTERS_BUTTONS_SELECT_ALL,"Seleziona tutto"),N(O,o.FILTERS_BUTTONS_CLEAR,"Pulisci"),N(O,o.FILTERS_BUTTONS_OK,"OK"),N(O,o.FILTERS_BUTTONS_CANCEL,"Annulla"),N(O,o.FILTERS_BUTTONS_PLACEHOLDER_SEARCH,"Cerca"),N(O,o.FILTERS_BUTTONS_PLACEHOLDER_VALUE,"Valore"),N(O,o.FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE,"Sostituisci con"),O);e.default.languages.registerLanguageDictionary(I),T.default=I}}).___});
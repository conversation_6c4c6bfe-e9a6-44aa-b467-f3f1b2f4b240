!function(E,T){if("object"==typeof exports&&"object"==typeof module)module.exports=T(require("../../handsontable"));else if("function"==typeof define&&define.amd)define(["../../handsontable"],T);else{var _=T("object"==typeof exports?require("../../handsontable"):E.Handsontable);for(var N in _)("object"==typeof exports?exports:E)[N]=_[N]}}("undefined"!=typeof self?self:this,function(E){return function(E){function T(N){if(_[N])return _[N].exports;var e=_[N]={i:N,l:!1,exports:{}};return E[N].call(e.exports,e,e.exports,T),e.l=!0,e.exports}var _={};return T.m=E,T.c=_,T.d=function(E,_,N){T.o(E,_)||Object.defineProperty(E,_,{configurable:!1,enumerable:!0,get:N})},T.n=function(E){var _=E&&E.__esModule?function(){return E.default}:function(){return E};return T.d(_,"a",_),_},T.o=function(E,T){return Object.prototype.hasOwnProperty.call(E,T)},T.p="",T(T.s=13)}({0:function(T,_){T.exports=E},13:function(E,T,_){"use strict";function N(E,T,_){return T in E?Object.defineProperty(E,T,{value:_,enumerable:!0,configurable:!0,writable:!0}):E[T]=_,E}T.__esModule=!0,T.default=void 0;var e,O=function(E){return E&&E.__esModule?E:{default:E}}(_(0)),I=O.default.languages.dictionaryKeys,r=(e={languageCode:"pt-BR"},N(e,I.CONTEXTMENU_ITEMS_ROW_ABOVE,"Inserir linha acima"),N(e,I.CONTEXTMENU_ITEMS_ROW_BELOW,"Inserir linha abaixo"),N(e,I.CONTEXTMENU_ITEMS_INSERT_LEFT,"Inserir coluna esquerda"),N(e,I.CONTEXTMENU_ITEMS_INSERT_RIGHT,"Inserir coluna direita"),N(e,I.CONTEXTMENU_ITEMS_REMOVE_ROW,["Excluir linha","Excluir linhas"]),N(e,I.CONTEXTMENU_ITEMS_REMOVE_COLUMN,["Excluir coluna","Excluir colunas"]),N(e,I.CONTEXTMENU_ITEMS_UNDO,"Desfazer"),N(e,I.CONTEXTMENU_ITEMS_REDO,"Refazer"),N(e,I.CONTEXTMENU_ITEMS_READ_ONLY,"Somente leitura"),N(e,I.CONTEXTMENU_ITEMS_CLEAR_COLUMN,"Limpar coluna"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT,"Alinhamento"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_LEFT,"Esquerda"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_CENTER,"Centralizado"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT,"Direita"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY,"Justificado"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_TOP,"Superior"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE,"Meio"),N(e,I.CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM,"Inferior"),N(e,I.CONTEXTMENU_ITEMS_FREEZE_COLUMN,"Congelar coluna"),N(e,I.CONTEXTMENU_ITEMS_UNFREEZE_COLUMN,"Descongelar coluna"),N(e,I.CONTEXTMENU_ITEMS_BORDERS,"Bordas"),N(e,I.CONTEXTMENU_ITEMS_BORDERS_TOP,"Superior"),N(e,I.CONTEXTMENU_ITEMS_BORDERS_RIGHT,"Direita"),N(e,I.CONTEXTMENU_ITEMS_BORDERS_BOTTOM,"Inferior"),N(e,I.CONTEXTMENU_ITEMS_BORDERS_LEFT,"Esquerda"),N(e,I.CONTEXTMENU_ITEMS_REMOVE_BORDERS,"Excluir bordas(s)"),N(e,I.CONTEXTMENU_ITEMS_ADD_COMMENT,"Incluir comentário"),N(e,I.CONTEXTMENU_ITEMS_EDIT_COMMENT,"Editar comentário"),N(e,I.CONTEXTMENU_ITEMS_REMOVE_COMMENT,"Remover comentário"),N(e,I.CONTEXTMENU_ITEMS_READ_ONLY_COMMENT,"Comentário somente leitura"),N(e,I.CONTEXTMENU_ITEMS_MERGE_CELLS,"Mesclar células"),N(e,I.CONTEXTMENU_ITEMS_UNMERGE_CELLS,"Desfazer mesclagem de células"),N(e,I.CONTEXTMENU_ITEMS_COPY,"Copiar"),N(e,I.CONTEXTMENU_ITEMS_CUT,"Recortar"),N(e,I.CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD,"Inserir linha filha"),N(e,I.CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD,"Desanexar da linha pai"),N(e,I.CONTEXTMENU_ITEMS_HIDE_COLUMN,["Ocultar coluna","Ocultar colunas"]),N(e,I.CONTEXTMENU_ITEMS_SHOW_COLUMN,["Exibir coluna","Exibir colunas"]),N(e,I.CONTEXTMENU_ITEMS_HIDE_ROW,["Ocultar linha","Ocultar linhas"]),N(e,I.CONTEXTMENU_ITEMS_SHOW_ROW,["Exibir linha","Exibir linhas"]),N(e,I.FILTERS_CONDITIONS_NONE,"Nenhum"),N(e,I.FILTERS_CONDITIONS_EMPTY,"É vazio"),N(e,I.FILTERS_CONDITIONS_NOT_EMPTY,"Não é vazio"),N(e,I.FILTERS_CONDITIONS_EQUAL,"É igual a"),N(e,I.FILTERS_CONDITIONS_NOT_EQUAL,"É diferente de"),N(e,I.FILTERS_CONDITIONS_BEGINS_WITH,"Começa com"),N(e,I.FILTERS_CONDITIONS_ENDS_WITH,"Termina com"),N(e,I.FILTERS_CONDITIONS_CONTAINS,"Contém"),N(e,I.FILTERS_CONDITIONS_NOT_CONTAIN,"Não contém"),N(e,I.FILTERS_CONDITIONS_GREATER_THAN,"Maior que"),N(e,I.FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL,"Maior ou igual a"),N(e,I.FILTERS_CONDITIONS_LESS_THAN,"Menor que"),N(e,I.FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL,"Maior ou igual a"),N(e,I.FILTERS_CONDITIONS_BETWEEN,"Está entre"),N(e,I.FILTERS_CONDITIONS_NOT_BETWEEN,"Não está entre"),N(e,I.FILTERS_CONDITIONS_AFTER,"Depois"),N(e,I.FILTERS_CONDITIONS_BEFORE,"Antes"),N(e,I.FILTERS_CONDITIONS_TODAY,"Hoje"),N(e,I.FILTERS_CONDITIONS_TOMORROW,"Amanhã"),N(e,I.FILTERS_CONDITIONS_YESTERDAY,"Ontem"),N(e,I.FILTERS_VALUES_BLANK_CELLS,"Células vazias"),N(e,I.FILTERS_DIVS_FILTER_BY_CONDITION,"Filtrar por condição"),N(e,I.FILTERS_DIVS_FILTER_BY_VALUE,"Filtrar por valor"),N(e,I.FILTERS_LABELS_CONJUNCTION,"E"),N(e,I.FILTERS_LABELS_DISJUNCTION,"Ou"),N(e,I.FILTERS_BUTTONS_SELECT_ALL,"Selecionar tudo"),N(e,I.FILTERS_BUTTONS_CLEAR,"Limpar"),N(e,I.FILTERS_BUTTONS_OK,"OK"),N(e,I.FILTERS_BUTTONS_CANCEL,"Cancelar"),N(e,I.FILTERS_BUTTONS_PLACEHOLDER_SEARCH,"Localizar"),N(e,I.FILTERS_BUTTONS_PLACEHOLDER_VALUE,"Valor"),N(e,I.FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE,"Segundo valor"),e);O.default.languages.registerLanguageDictionary(r),T.default=r}}).___});
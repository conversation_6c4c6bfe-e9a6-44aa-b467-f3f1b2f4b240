<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="<%= process.env.VUE_APP_BASE_URL %>/favicon.ico">
<!--  <% if (process.env.NODE_ENV==='production' ) { %>-->
<!--  <link rel="icon" href="<%= process.env.VUE_APP_BASE_URL %>/favicon_pro.ico">-->
<!--  <% }else{ %>-->
<!--  <link rel="icon" href="<%= process.env.VUE_APP_BASE_URL %>/favicon.ico">-->
<!--  <% } %>-->
  <link rel="stylesheet" href="<%= process.env.VUE_APP_BASE_URL %>/assets/styles/element-zjui/lib/index.css?v=0507">
  <script src="https://g.alicdn.com/IMM/office-js/1.1.19/aliyun-web-office-sdk.min.js"></script>
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/d3@5.16.0/d3.v5.min.js?v=0507"></script>
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/dagre-d3@0.6.5/dagre-d3.min.js?v=0507"></script>
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/echarts/echarts.min.js?v=0507"></script>

  <% if (process.env.NODE_ENV==='production' ) { %>
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/vue@2.7.14/vue.min.js?v=0507"></script>
  <% }else{ %>
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/vue@2.7.14/vue.js?v=0507"></script>
  <% } %>
  <link href="<%= process.env.VUE_APP_BASE_URL %>/assets/handsontable@6.2.2/dist/handsontable.full.min.css?v=0507" rel="stylesheet">
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/handsontable@6.2.2/dist/handsontable.full.min.js?v=0507"></script>
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/handsontable@6.2.2/dist/languages/zh-CN.min.js?v=0507"></script>
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/handsontable-vue/dist/vue-handsontable.min.js?v=0507"></script>
  <!-- 富文本编辑器 -->
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/tinymce@4.9.3/tinymce.min.js?v=0507" referrerpolicy="origin"></script>
<!--  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/tinymce/tinymce.min.js?v=0507" referrerpolicy="origin"></script>-->
  <!-- 百度地图插件 -->
  <script type="text/javascript" src="//api.map.baidu.com/api?v=2.0&ak=7VmSeDf2DLt6uAssxna6s0VN5SP3fKIb"></script>
  <!-- 天地图插件 -->
  <script type="text/javascript" src="//api.tianditu.gov.cn/api?v=4.0&tk=24571ab2498ddde3e0d1a40cb118979a"></script>
  <!--  <script  src="//at.alicdn.com/t/font_1456930_3k6jai6djb4.js?v=0507"></script>-->
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/al-font/font_1456930_3k6jai6djb4.js?v=0507"></script>
  <!-- canvas转图片插件  -->
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/html2canvas@1.4.1/html2canvas.min.js?v=0507"></script>
  <!-- 区域数据 -->
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/element-china-area-data/app.js?v=0507"></script>
  <!-- 轮播组件  -->
  <link href="<%= process.env.VUE_APP_BASE_URL %>/assets/swiper@8.4.2/swiper-bundle.min.css?v=0507" rel="stylesheet">
  <script  src="<%= process.env.VUE_APP_BASE_URL %>/assets/swiper@8.4.2/swiper-bundle.min.js?v=0507"></script>
  <!-- 畅写服务 -->
<!--  <script  src="<%= process.env.VUE_APP_CX %>/web-apps/apps/api/documents/api.js?v=0507" integrity="sha384-MR4LJV+rA99yF7imAzxYckiVA5WINL7cukQeX37zGxKITBWGQ/5cvPaGhuCMaLKr" crossorigin="anonymous"></script>-->
  <!-- 汇付 -->
    <script src="https://cloudpnrcdn.oss-cn-shanghai.aliyuncs.com/opps/ui/data/areaList.js?v=0507" integrity="sha384-q0wVFdo9/4cKrNBdQ1oUNag5cP766+MSsrV7iLHLqEZXm6G8/jWYywbBnwjHof1p" crossorigin="anonymous"></script>
    <script src="https://cloudpnrcdn.oss-cn-shanghai.aliyuncs.com/opps/ui/data/unionMccList.js?v=0507" integrity="sha384-MXa8Hz3fhgvFRYWBSuH60YVPImrtN69uenZmAqEdVps/avH4/KTobJ19RWPpHJrv" crossorigin="anonymous"></script>
<!--    <script src="https://cloudpnrcdn.oss-cn-shanghai.aliyuncs.com/opps/ui/data/banks.js?v=0507" integrity="sha384-L+taTywa61PSpt8KaVrAIZqQe5ciWhirzOBp3RwyVQ7p0LRI0suymvKnzZfrReM+" crossorigin="anonymous"></script>-->
    <script src="<%= process.env.VUE_APP_BASE_URL %>/assets/bank/bank.js?v=0507" ></script>
    <% if (process.env.NODE_ENV==='production' ) { %>
    <script src="https://tam.cdn-go.cn/aegis-sdk/latest/aegis.min.js?v=0507"></script>

    <script>
    if (typeof Aegis === 'function') {
      var aegis = new Aegis({
        id: '6o2oPhlnQ6VZrPZ6nw', // 上报 id
        uin: 'xxx', // 用户唯一 ID（可选）
        reportApiSpeed: true, // 接口测速
        reportAssetSpeed: true, // 静态资源测速
        spa: true, // spa 应用页面跳转的时候开启 pv 计算
        hostUrl: 'https://rumt-zh.com',
        env:'<%=VUE_APP_AEGIS_ENV %>',
        api: {
          apiDetail: true,
          reqHeaders: ['Authorization'],
          retCodeHandler(data, url, xhr) {
            // data 是string类型，如果需要对象需要手动parse下
            // url 为请求url
            // xhr 响应,可以通过xhr.response拿到完整的后台响应
            try {
              data = JSON.parse(data)
            } catch (e) {}
            return {
              // isErr 如果是 true 的话，会上报一条 retcode 异常的日志。
              isErr: (data.code &&data.code !== 3001) || ( data.state === 'error'),
              code: data.code || data.state
            }
          }
        }
      });
    }
    </script>
    <% } %>
  <title>
    <%= webpackConfig.name %>
  </title>

  <% if (process.env.VUE_APP_MODE === 'tel') { %>
  <style>
    .sidebar-container{
      display: none;
    }
    .fixed-header{
      display: none;
    }
    .main-container{
      margin: 0 !important;
    }
    .app-main{
      padding-top: 16px !important;
    }
  </style>
  <% } %>
</head>

<body >
  <div id="app"></div>
</body>

</html>

import { defineConfig, loadEnv, UserConfig } from 'vite'
import vue2 from '@vitejs/plugin-vue2'
import { createHtmlPlugin } from 'vite-plugin-html'
import vueJsx from '@vitejs/plugin-vue2-jsx'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

import { viteExternalsPlugin } from 'vite-plugin-externals'

import { compression } from 'vite-plugin-compression2'

import path from 'path'

import vitePluginFile from './misc/vite-plugin-file.js'

const name = '客服物业' // page title
function resolve(dir) {
  return path.join(__dirname, dir)
}
// https://vitejs.dev/config/
export default defineConfig(({ command, mode, build }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀。
  // const envPrefix = ['VUE']
  const env = loadEnv(mode, process.cwd(), '')

  const define = {
    'process.env.VUE_APP_BASE_API': '/dev-api',
    'process.env.VUE_APP_BASE_URL': '/',
    'process.env.VUE_APP_CX': '/',
    'process.env.ENV': '',
    __APP_ENV__: env.APP_ENV
  }

  for (const [key, value] of Object.entries(env)) {
    define[`process.env.${key}`] = value
  }

  const prependData = '@import "@/styles/element-variables.scss";'

  // 代理地址
  // const proxy_url = 'https://wy-test.c-land.com'
  const proxy_url = 'https://dev-kfwy.youmatech.com'
  // const proxy_url = 'https://wy.c-land.com'
  // const proxy_url = 'https://reip-cspm-uat.cscec.com'
  return {
    base: define['process.env.VUE_APP_BASE_URL'] || '/', // 静态资源目录
    server: {
      port: 9527,
      proxy: {
        '/dev-api': {
          target: proxy_url,
          secure: false,
          headers: {
            Origin: proxy_url
          },
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev-api/, '')
        },
        '/api/file/system/upload/resource/v2': {
          target: proxy_url,
          secure: false,
          headers: {
            Origin: proxy_url
          },
          changeOrigin: true
        },
        '/api/file/system/file/imageCut': {
          target: proxy_url,
          secure: false,
          headers: {
            Origin: proxy_url
          },
          changeOrigin: true
        }
        // '/n0': {
        //   target: proxy_url,
        //   secure: false,
        //   headers: {
        //     Origin: proxy_url
        //   },
        //   changeOrigin: true
        // },
        // '/dev2-api': {
        //   target: 'https://k8s-a6.7x24cc.com',
        //   secure: false,
        //   headers: {
        //     Origin: 'https://k8s-a6.7x24cc.com'
        //   },
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/dev2-api/, '')
        // }
      }
    },
    resolve: {
      alias: {
        '@': resolve('src'),
        '~@': resolve('src'),
        '~@element-zjui': resolve('node_modules/element-zjui')
      },
      extensions: ['.js', '.vue', '.mjs', '.mts', '.ts', '.jsx', '.tsx', '.json']
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: prependData
        }
      }
    },
    plugins: [
      vue2(),
      vueJsx({
        include: /\.[jt]sx$/
      }),
      createSvgIconsPlugin({
        // 存放 svg 文件的文件夹
        iconDirs: [path.resolve(process.cwd(), 'src/icons/svg')],
        // 和 webpack 保持一致就行
        symbolId: 'icon-[name]',
        inject: 'body-first'
      }),
      createHtmlPlugin({
        // 在这里配置模板语法
        template: 'index.html',
        entry: '/src/main.js',
        inject: {
          data: {
            title: 'title',
            VUE_APP_AEGIS_ENV: env.VUE_APP_AEGIS_ENV,
            webpackConfig: {
              name: name
            },
            process: {
              env: {
                NODE_ENV: define['process.env.NODE_ENV'],
                VUE_APP_BASE_URL: define['process.env.VUE_APP_BASE_URL'],
                VUE_APP_CDN: define['process.env.VUE_APP_CDN'],
                VUE_APP_MODE: define['process.env.VUE_APP_MODE'],
                VUE_APP_CX: define['process.env.VUE_APP_CX'],
                VUE_APP_ENV: define['process.env.VUE_APP_ENV']
              }
            }
          }

        }
      }),
      viteExternalsPlugin({
        vue: 'Vue'
      }),
      compression(),
      vitePluginFile({
        mode: mode,
        VUE_APP_ZIP_NAME: env.VUE_APP_ZIP_NAME
      })
    ],
    // vite 配置
    define: {
      'process.env.VITE': true,
      'process.env.NODE_ENV': JSON.stringify(define['process.env.NODE_ENV']),
      'process.env.VUE_APP_BASE_URL': JSON.stringify(define['process.env.VUE_APP_BASE_URL']),
      'process.env.VUE_APP_BASE_API': JSON.stringify(define['process.env.VUE_APP_BASE_API']),
      'process.env.VUE_APP_MODE': JSON.stringify(define['process.env.VUE_APP_MODE']),
      'process.env.VUE_APP_CX': JSON.stringify(define['process.env.VUE_APP_CX']),
      'process.env.ENV': JSON.stringify(define['process.env.ENV'])
    },
    build: {
      assetsDir: +new Date() + '',
      sourcemap: 'hidden',
      rollupOptions: {
        // external: ['vue'],
        // output: {
        //   // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
        //   globals: {
        //     vue: 'Vue'
        //   }
        // }
      }
    }
  }
})

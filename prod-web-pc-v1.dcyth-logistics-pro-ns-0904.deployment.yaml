kind: Deployment
apiVersion: apps/v1
metadata:
  name: prod-web-pc-v1
  namespace: dcyth-logistics-pro-ns-0904
  labels:
    app: prod-web-pc
    app.kubernetes.io/name: cspm-web
    app.kubernetes.io/version: v1
    sidecar.istio.io/inject: 'false'
    version: v1
  annotations:
    cloudbases.io/creator: dcyth-logistics-prod-user
    deployment.kubernetes.io/revision: '1'
    servicemesh.cloudbases.io/enabled: 'false'
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prod-web-pc
      app.kubernetes.io/name: cspm-web
      app.kubernetes.io/version: v1
      sidecar.istio.io/inject: 'false'
      version: v1
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: prod-web-pc
        app.kubernetes.io/name: cspm-web
        app.kubernetes.io/version: v1
        sidecar.istio.io/inject: 'false'
        version: v1
      annotations:
        cloudbases.io/containerSecrets: '{"container-ubrz00":"harbor"}'
        sidecar.istio.io/inject: 'false'
    spec:
      containers:
        - name: container-ubrz00
          image: '$REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$APP_ENV-$BUILD_NUMBER'
          ports:
            - name: http-pc
              containerPort: 80
              protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      imagePullSecrets:
        - name: harbor
      affinity: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600

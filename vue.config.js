'use strict'
const path = require('path')
const moment = require('moment')
const { defineConfig } = require('@vue/cli-service')

const FileManagerPlugin = require('filemanager-webpack-plugin') // 压缩为 zip
const CompressionPlugin = require('compression-webpack-plugin') // 压缩gzip
const packageInfo = require('./package.json')

function resolve(dir) {
  return path.join(__dirname, dir)
}
// 打印环境变量
// console.log(process.env)

const name = '中建客服物业系统' // page title

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following method:
// port = 9527 npm run dev OR npm run dev --port = 9527
const port = process.env.port || process.env.npm_config_port || 9527 // dev port
const timestamp = moment().format('MM_DD_HH_mm')
// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: process.env.VUE_APP_BASE_URL || '/',
  outputDir: 'dist',
  assetsDir: 'static/v' + process.env.npm_package_version + '.' + timestamp,
  lintOnSave: process.env.NODE_ENV !== 'production',
  productionSourceMap: process.env.NODE_ENV !== 'production',
  pages: {
    index: {
      entry: 'src/main.js',
      template: 'index.html'
    }
  },
  devServer: {
    port: port,
    open: true,
    // devMiddleware:{
    //   overlay: {
    //     warnings: false,
    //     errors: true
    //   }
    // },
    proxy: {
      '/dev-api': {
        target: 'https://zjyth.youmatech.com/',
        changeOrigin: true,
        pathRewrite: {
          ['^' + '/dev-api']: ''
        }
      },
      '/api': {
        target: 'https://zjyth.youmatech.com/',
        changeOrigin: true
      }
    }
  },
  configureWebpack: config => {
    const obj = {
      name: name,
      resolve: {
        alias: {
          '@': resolve('src')
        }
      }
    }
    // 默认排除依赖
    const externals = {
      vue: 'Vue'
    }
    // 生产环境追加
    if (process.env.NODE_ENV === 'production') {
      Object.assign(obj, {
        plugins: [
          new CompressionPlugin(), // 打包gzip
          new FileManagerPlugin({
            events: {
              onEnd: {
                archive: [{ source: './dist', destination: `.${process.env.VUE_APP_BASE_URL}.${packageInfo.version}.zip` }]
              }
            }
          })
        ]
      })
    } else {
      // 开发环境追加
      Object.assign(obj, {
        devtool: 'eval-source-map'
      })
    }
    return {
      ...obj,
      externals
    }
  },
  chainWebpack(config) {
    config.module
      .rule('strip')
      .test(/\.jsx?$/)
      .include.add(resolve('src'))
      .end()
      .use('webpack-strip-block')
      .loader('webpack-strip-block')
      .options({
        start: 'develblock:start',
        end: 'develblock:end'
      })
      .end()

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  },
  css: {
    loaderOptions: {
      sass: {
        prependData: '@import "~@/styles/element-variables.scss";',
        sassOptions: {
          outputStyle: 'expanded'
        }
      }
    }
  }
}

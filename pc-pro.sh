#!/bin/sh

number2=$(date +%Y%m%d)
echo $number2


#配置环境变量
export REGISTRY=harbor.cscec.com;
export DOCKERHUB_NAMESPACE=cscec-cspm;
export APP_NAME=cspm-pc;
export APP_ENV=pro-loc;
export BUILD_NUMBER=$number2;
node -v;
rm -rf ./phtml.*


npm run build:prod;
docker build -t $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$APP_ENV-$BUILD_NUMBER .;
docker push  $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$APP_ENV-$BUILD_NUMBER;


echo $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$APP_ENV-$BUILD_NUMBER;

git reset --hard
# git pull;

# find ./cspm-pc-v1.dcyth-logistics-pro-ns-0904.deployment.yaml -type f |while read FILE;
#   do envsubst < "$FILE" > "$FILE".tmp&&mv "$FILE".tmp "$FILE";
# done;

# kubectl apply -f ./cspm-pc-v1.dcyth-logistics-pro-ns-0904.deployment.yaml

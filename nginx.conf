user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    server_tokens   off; # 关闭nginx 版本号
    #tcp_nopush     on;

    keepalive_timeout  65;

    gzip  on;
    server {
        listen       80;
        server_name  _;

        location / {
            if ($request_method = 'OPTIONS') {
                return 204;
            }
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

            alias /usr/local/youmatech/zjyth/static/phtml/;
            try_files $uri $uri/ /index.html;
            if ($request_filename ~* .*\.(?:htm|html)$) {
              add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
           }
           error_page 403 =404 /404.html;
        }

    }

    # include /etc/nginx/conf.d/*.conf;
}



kind: Deployment
apiVersion: apps/v1
metadata:
  name: test-pc-v1
  namespace: dcyth-logistics-uat-ns-0615
  labels:
    app: test-pc
    version: v1
  annotations:
    cloudbases.io/creator: dcyth-logistics-uat-user
    deployment.kubernetes.io/revision: '12'
spec:
  replicas: 1
  selector:
    matchLabels:
      app: test-pc
      version: v1
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: test-pc
        version: v1
      annotations:
        cloudbases.io/containerSecrets: '{"container-z3uv7g":"harbor"}'
        cloudbases.io/restartedAt: '2023-08-01T05:40:34.398Z'
    spec:
      containers:
        - name: container-z3uv7g
          image: '$REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$APP_ENV-$BUILD_NUMBER'
          ports:
            - name: http-pc
              containerPort: 80
              protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      imagePullSecrets:
        - name: harbor
      affinity: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600

#!/bin/sh

number2=$(date +%s)
echo $number2


#配置环境变量
export REGISTRY=harbor.cscec.com;
export DOCKERHUB_NAMESPACE=cscec-cspm;
export APP_NAME=cspm-pc;
export APP_ENV=uat-loc;
export BUILD_NUMBER=$number2;
node -v;
rm -rf ./phtml.*


npm run build;
docker build -t $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$APP_ENV-$BUILD_NUMBER .;
docker push  $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$APP_ENV-$BUILD_NUMBER;



echo $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$APP_ENV-$BUILD_NUMBER;

git reset --hard
# git pull;

# find ./Jenkinsfile.yaml -type f |while read FILE;
#   do envsubst < "$FILE" > "$FILE".tmp&&mv "$FILE".tmp "$FILE";
# done;

<template>
  <div class="full-height">
    <el-row type="flex" class="full-width full-height">
      <el-col :span="7" class="f-mr16 width-380">
        <um-block title="组织架构" full class="full-height" content-class="f-flex-col">
          <template #tool>
            <el-button v-permission="['00100014000300010016']" type="primary" @click="tbData">同步组织</el-button>
          </template>
          <div class="f-m-16 flex">
            <!--            暂时不需要前端做筛选-->
            <!--            @change="$refs.tree.filter(filterText)-->
            <el-select v-model="organSource" class="option" style="width: 100px" @change="getTree">
              <el-option :value="2" label="全部" />
              <el-option :value="0" label="新增" />
              <el-option :value="1" label="同步" />
            </el-select>
            <el-input v-model="filterText" placeholder="请输入组织名称搜索" clearable />
          </div>
          <div class="full-height f-flex-1 m_tree_l">
            <el-tree
              ref="tree"
              :data="treeData"
              :props="defaultProps"
              node-key="orgOrganId"
              highlight-current
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              :default-expanded-keys="idArr"
              @current-change="currentTreeChange"
            >
              <span
                slot-scope="{ node, data }"
                class="custom-tree-node"
                :class="{ isOnly: data.readOnlyFlag }"
              >
                <span v-if="data.organTypeName">
                  <el-tag
                    v-if="data.typeCode=='1001501'"
                    size="mini"
                    :style="{
                      color: '#fff',
                      background: '#0081CC',
                      borderColor: '#0081CC',
                      lineHeight:'18px',
                    }"
                  >{{ data.organTypeName }}</el-tag>
                  <el-tag
                    v-if="data.typeCode=='1001502'"
                    size="mini"
                    :style="{
                      color: '#fff',
                      background: '#4CA6DB',
                      borderColor: '#4CA6DB',
                      lineHeight:'18px',
                    }"
                  >{{ data.organTypeName }}</el-tag>
                  <el-tag
                    v-if="data.typeCode=='1001503'"
                    size="mini"
                    :style="{
                      color: '#fff',
                      background: '#99CDEB',
                      borderColor: '#99CDEB',
                      lineHeight:'18px',
                    }"
                  >{{ data.organTypeName }}</el-tag>
                  <el-tag
                    v-if="data.typeCode=='1001504'"
                    size="mini"
                    :style="{
                      color: '#fff',
                      background: '#98C2D8',
                      borderColor: '#98C2D8',
                      lineHeight:'18px',
                    }"
                  >{{ data.organTypeName }}</el-tag>
                </span>
                <span class="dib">
                  <el-tooltip
                    effect="light"
                    :content="node.label"
                    placement="top"
                    :disabled="isShowTooltip"
                  >
                    <div class="f-ml4" @mouseover="onMouseOver(node.key)">
                      <span :ref="node.key">{{ node.label }}</span>
                    </div>
                  </el-tooltip>
                </span>

                <el-popover
                  ref="popover"
                  placement="right"
                  width="104"
                  trigger="hover"
                  :visible-arrow="false"
                  popper-class="popper-class"
                >
                  <ul class="btn-box">
                    <li
                      v-permission="['00100014000300010012']"
                      class="btn-item"
                      @click.stop="() => append(node, data)"
                    ><i class="el-icon-circle-plus-outline" /> 新建</li>
                    <li
                      v-permission="['00100014000300010013']"
                      class="btn-item"
                      @click.stop="() => update(node, data)"
                    ><i class="el-icon-edit-outline" /> 编辑</li>
                    <li
                      v-permission="['00100014000300010014']"
                      class="btn-item"
                      @click.stop="() => remove(node, data)"
                    ><i class="el-icon-delete" /> 删除</li>
                  </ul>

                  <!--                <span-->
                  <!--                  v-if="node.isCurrent && data.readOnlyFlag !== 1"-->
                  <!--                  class="edit"-->
                  <!--                  :class="{ on: node.isCurrent && data.readOnlyFlag !== 1 }"-->
                  <!--                >-->
                  <!--                  <el-tooltip effect="light" content="添加" placement="top">-->
                  <!--                    <el-button-->
                  <!--                      v-permission="['00100014000300010002']"-->
                  <!--                      type="text"-->
                  <!--                      size="mini"-->
                  <!--                      @click.stop="() => append(node, data)"-->
                  <!--                    >-->
                  <!--                      <i class="el-icon-plus" />-->
                  <!--                    </el-button>-->
                  <!--                  </el-tooltip>-->
                  <!--                  <el-tooltip effect="light" content="编辑" placement="top">-->
                  <!--                    <el-button-->
                  <!--                      v-permission="['00100014000300010003']"-->
                  <!--                      type="text"-->
                  <!--                      size="mini"-->
                  <!--                      @click.stop="() => update(node, data)"-->
                  <!--                    >-->
                  <!--                      <i class="el-icon-edit" />-->
                  <!--                    </el-button>-->
                  <!--                  </el-tooltip>-->
                  <!--                  <el-tooltip-->
                  <!--                    v-if="node.level > 1"-->
                  <!--                    effect="light"-->
                  <!--                    content="删除"-->
                  <!--                    placement="top"-->
                  <!--                  >-->
                  <!--                    <el-button-->
                  <!--                      v-permission="['00100014000300010004']"-->
                  <!--                      type="text"-->
                  <!--                      size="mini"-->
                  <!--                      @click.stop="() => remove(node, data)"-->
                  <!--                    >-->
                  <!--                      <i class="el-icon-delete" />-->
                  <!--                    </el-button>-->
                  <!--                  </el-tooltip>-->
                  <!--                </span>-->
                  <span slot="reference" class="f-flex-ac">
                    <img
                      v-if="node.isCurrent && data.readOnlyFlag !== 1"
                      src="@/assets/images/ellipsisH.png"
                      class="img-icon"
                    >
                    <!--                  <img v-else src="@/assets/images/ellipsis.png" class="img-icon">-->
                    <!--                  <i class="el-icon-more img-icon"></i>-->
                  </span>
                </el-popover>

              </span>
            </el-tree>
          </div>
        </um-block>
      </el-col>
      <el-col :span="17" class="f-flex-1">
        <um-block title="详细信息" class="full-height scroll-y">
          <um-card title="基本信息" :col="2">
            <um-card-item label="上级组织" :value="organInfo.parentOrgOrganName" />
            <um-card-item label="组织类型" :value="organInfo.orgOrganTypeName" />
            <um-card-item label="组织名称" :value="organInfo.organFullName" />
            <um-card-item label="所在城市" :value="organInfo.organCityName" />
            <um-card-item label="详细地址" :value="organInfo.organAddress" />
            <um-card-item label="数据来源" :value="organInfo.organSource">
              <template #default>
                <span>
                  {{ organInfo.organSource?'同步':'自建' }}
                </span>
              </template>
            </um-card-item>
            <um-card-item label="说明" :value="organInfo.organRemark" :span="24" />
          </um-card>
          <user :org-organ-id="searchParam.orgOrganId" :bus-organ-obj="busOrganObj" :user-organ-ids="idArr" />
        </um-block>
      </el-col>
    </el-row>
    <el-dialog
      :title="addForm.status === 'add' ? '新增组织' : '编辑组织'"
      :visible.sync="addVisible"
      width="60%"
      @close="$refs.addForm.resetFields()"
    >
      <el-form
        ref="addForm"
        :model="addForm"
        label-width="100px"
        :rules="addRules"
        class="um-add_form"
        @submit.native.prevent
      >
        <el-form-item label="上级组织">{{ addForm.parentOrganFullName }}</el-form-item>
        <el-form-item
          v-if="addForm.status === 'add'"
          ref="orgOrganTypeId"
          label="组织类型"
          prop="orgOrganTypeId"
        >
          <el-select
            v-model="addForm.orgOrganTypeId"
            placeholder="选择组织类型"
            class="full-width"
            clearable
          >
            <el-option
              v-for="item in orgOrganTypeList"
              :key="item.value"
              :value="item.value"
              :label="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item ref="organName" label="组织名称" prop="organName">
          <el-input
            v-model="addForm.organName"
            placeholder="请输入名称"
            maxlength="50"
            class="full-width"
          />
        </el-form-item>
        <el-form-item
          v-if="orgOrganTypeName!=='部门'"
          ref="organCityCode"
          label="所在城市"
          prop="organCityCode"
        >
          <el-cascader
            v-model="addForm.organCityCode"
            filterable
            :options="cityOptions"
            :props="{ value: 'adcode', label: 'name',children:'children',emitPath: false}"
            class="full-width"
          />
        </el-form-item>
        <el-form-item
          v-if="orgOrganTypeName!=='部门'"
          ref="organAddress"
          label="详细地址"
          prop="organAddress"
        >
          <el-row type="flex">
            <el-input
              v-model="addForm.organAddress"
              maxlength="50"
              placeholder="请选择组织地址"
            />
            <el-button
              type="text"
              class="f-ml10"
              @click="openMap"
            >
              从地图选
            </el-button>
          </el-row>
        </el-form-item>
        <el-form-item ref="organRemark" label="组织说明" prop="organRemark">
          <el-input
            v-model="addForm.organRemark"
            type="textarea"
            placeholder="请输入说明，最多500字"
            maxlength="500"
            show-word-limit
            class="full-width"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="f-mr8" @click="addVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="() => (addForm.status === 'add' ? addSubmit() : updateSubmit())"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="mapVisible"
      :destroy-on-close="true"
      width="840px"
      title="选择组织地址"
      append-to-body
    >
<!--      <um-map ref="umMap" v-model="activityLatLngBaidu" :readonly="false" />-->
      <div slot="footer" class="dialog-footer text-center">
        <el-button class="f-mr8" type="primary" @click="mapSubmit">保 存</el-button>
        <el-button @click="mapVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import User from './user'
import UmMap from '@/components/UmMap'
import UmBlock from '@/components/UmBlock'
import UmCard from '@/components/UmCard'
import UmCardItem from '@/components/UmCardItem'
import {
  queryOrganTree,
  queryOrganBaseInfo,
  addOrgan,
  selectOrganInfoByOrganId,
  updateOrganCommit,
  getOrganTypeByOrganId,
  deleteOrganByOrganId
} from '@/api/system/organManage'
import { reverseGeocoding, transformBD } from '@/utils'
import { organOrganSync } from '@/api/common'
import city from '@/utils/city'
// const regionData = window.regionData
export default {
  name: 'OrganManageOrgan',
  components: { User, UmMap, UmBlock, UmCard, UmCardItem },
  props: {},
  data() {
    return {
      city,
      cityName: '',
      cityOptions: city,
      idArr: [],
      filterText: '',
      searchParam: {
        orgOrganId: ''
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'organName'
        // disabled: (data, node) => {
        //   return data.readOnlyFlag
        // }
      },
      organInfo: {}, // 组织信息
      addVisible: false,
      busOrganObj: {}, //  label value
      addForm: {
        parentOrganFullName: '', // 上级组织 名称
        status: 'add', // add 为新增  edit 为编辑
        orgOrganTypeName: '',
        orgOrganTypeId: null, // 组织类型
        organName: '', // 组织名称
        organCityCode: '0000', // 所在城市
        organLngLatBaidu: '', // 百度地址 经纬度
        organLngLatHuoxing: '', // 火星地址 经纬度
        organAddress: '', // 详细地址
        organRemark: '', // 组织说明
        parentOrgOrganId: 0, // 上级组织id,无上级传0
        orgOrganId: null // 组织ID  编辑时用
      },
      addRules: {
        orgOrganTypeId: [{ required: true, message: '必填项不能为空', trigger: 'change' }],
        organName: [{ required: true, message: '必填项不能为空', trigger: 'blur' }, { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'change' }],
        organRemark: [{ max: 500, message: '长度在 0 到 500 个字符', trigger: 'change' }]
      },
      mapVisible: false,
      activityLatLngBaidu: {},
      orgOrganTypeList: [], // 组织架构类型
      treeDisabledArr: [],
      checkList: [],
      isShowTooltip: false,
      parentNode: null,
      organSource: 2 // 0 自建 1 同步 2 全部
    }
  },
  computed: {
    orgOrganTypeName() {
      let name = ''
      if (this.addForm.status === 'add') {
        this.orgOrganTypeList && this.orgOrganTypeList.length && this.orgOrganTypeList.forEach(item => {
          if (item.value === this.addForm.orgOrganTypeId) {
            name = item.name
          }
        })
        return name
      } else {
        return this.addForm.orgOrganTypeName
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    'searchParam.orgOrganId'(val) {
      // if (this.busOrganObj.readOnlyFlag !== 1) {
      this.getInfo()
      // } else {
      // this.organInfo = {}
      // }
    },
    mapVisible(val) {
      if (val) {
        if (this.addForm.status === 'add') {
          this.activityLatLngBaidu = {}
        }
        this.$nextTick(() => {
          this.$refs.umMap.init()
        })
      }
    }
  },
  created() { },
  mounted() {
    this.getTree(0, '')
  },
  methods: {
    tbData() {
      this.$message.warning('同步正在进行中 请勿重复请求')
      organOrganSync({ type: 1 }).then(res => {
        this.$message.success('操作成功')
      }).catch(e => {
        this.$errorHandle(e)
      })
    },
    onMouseOver(str) { // 内容超出，显示文字提示内容
      const tag = this.$refs[str]
      const parentWidth = tag.parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = tag.offsetWidth // 获取元素可视宽度
      this.isShowTooltip = contentWidth <= parentWidth
    },
    getTree(type, row) {
      queryOrganTree({ organSource: this.organSource }).then((d) => {
        this.treeData = d.data
        console.log(d.data, 11)

        // 初始展开树===
        this.treeData.forEach((item) => {
          this.idArr.push(item.orgOrganId)
        })
        this.$nextTick(() => {
          if (type === 1) {
            console.log(row)
            this.$refs.tree.setCurrentNode(row)
            this.searchParam.orgOrganId = row.orgOrganId // 搜索条件
            this.busOrganObj = row // 当前选中对象
          } else if (type === 0) {
            const node = this.findFirstNotReadNode(this.treeData)
            this.$refs.tree?.setCurrentNode(node)
            this.searchParam.orgOrganId = node.orgOrganId // 搜索条件
            this.busOrganObj = node // 当前选中对象
            this.idArr.push(node.orgOrganId)
          } else {
            const parentNode = this.findNodeByOrganId(this.treeData, row)
            console.log(parentNode, '888888')
            if (parentNode) {
              this.$refs.tree.setCurrentNode(parentNode) // 设置焦点到上一个节点
              this.searchParam.orgOrganId = parentNode.orgOrganId // 搜索条件
            }
          }
          this.idArr = Array.from(new Set(this.idArr)) // 去重
        })
      }).catch(() => {})
    },
    // 找到第一个readonly为 0 的节点
    findFirstNotReadNode(arr) {
      let node = null
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        const itemChildren = item.children
        if (!item.readOnlyFlag) {
          node = item
          break
        }
        if (Array.isArray(itemChildren) && itemChildren.length) {
          node = this.findFirstNotReadNode(itemChildren)
          if (node) {
            break
          }
        }
      }
      return node
    },
    findNodeByOrganId(nodes, organId) {
      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i]
        if (node.orgOrganId === organId) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const foundNode = this.findNodeByOrganId(node.children, organId)
          if (foundNode) {
            return foundNode
          }
        }
      }
      return null
    },
    currentTreeChange(row, node) {
      this.searchParam.orgOrganId = row.orgOrganId
      this.busOrganObj = row
      this.idArr = [row.orgOrganId]
    },
    filterNode(value, data) {
      // if (!value) return this.sourceCode === 2 || data.organSource === this.sourceCode
      // return data.organFullName.indexOf(value) !== -1 && (this.sourceCode === 2 || data.organSource === this.sourceCode)
      if (!value) return true
      return data.organFullName.indexOf(value) !== -1
    },
    // 获取组织详情
    getInfo() {
      queryOrganBaseInfo({ orgOrganId: this.searchParam.orgOrganId }).then(d => {
        this.organInfo = d.data
      }).catch(() => {})
    },
    openMap() {
      this.$locationTDT({
        value: {
          formatted_address: this.addForm.organLngLatBaidu ? this.addForm.organAddress : '',
          lng: this.addForm.organLngLatBaidu ? Number(this.addForm.organLngLatBaidu.split(',')[0]) : '',
          lat: this.addForm.organLngLatBaidu ? Number(this.addForm.organLngLatBaidu.split(',')[1]) : ''
        },
        resultHandler: (position) => {
          this.addForm.organAddress = position.formatted_address
          this.addForm.organLngLatHuoxing = position.gcj02_lng ? `${position.gcj02_lng},${position.gcj02_lat}` : ''
          this.addForm.organLngLatBaidu = position.lng ? `${position.lng},${position.lat}` : ''
        }
      })
    },
    // 地图回显
    async mapSubmit() {
      const latlng = this.activityLatLngBaidu
      console.log(latlng)
      if (!latlng.lat || !latlng.lng) {
        this.$message.info('请选择位置！')
        return false
      }
      try {
        const d2 = await reverseGeocoding(latlng.lng, latlng.lat)
        this.addForm.organAddress = d2.result.formatted_address
      } catch (e) {
        this.$message.error('获取位置失败！')
        return false
      }

      try {
        const d = await transformBD(latlng.lng, latlng.lat, 'gcj02')
        if (d.result && d.result[0]) {
          this.addForm.organLngLatHuoxing = `${d.result[0].x},${d.result[0].y}`
          this.addForm.organLngLatBaidu = `${latlng.lng},${latlng.lat}`
        } else {
          this.addForm.organLngLatHuoxing = ''
          this.addForm.organLngLatBaidu = ''
          this.$message.error('获取经纬度失败！')
        }
      } catch (e) {
        this.addForm.organLngLatHuoxing = ''
        this.addForm.organLngLatBaidu = ''
        this.$message.error('获取经纬度失败！')
        return false
      }
      this.mapVisible = false
    },
    async append(node, data) {
      console.log(node, 'node--')
      console.log(data, 'data--')
      try {
        this.addForm.status = 'add'
        this.addForm.parentOrganFullName = data.organFullName
        this.addForm.parentOrgOrganId = data.orgOrganId
        const orgOrganTypeList = await getOrganTypeByOrganId({
          parentOrgOrganId: data.orgOrganId
        })
        this.orgOrganTypeList = orgOrganTypeList.data
        this.addForm.orgOrganTypeId = null
        this.addVisible = true
      } catch (e) {
        this.$errorHandle(e)
      }
    },
    async update(node, data) {
      try {
        const res = await selectOrganInfoByOrganId({
          orgOrganId: data.orgOrganId
        })
        this.addForm.status = 'edit'
        this.addVisible = true
        this.$nextTick(() => {
          const keys = ['orgOrganId', 'orgOrganTypeName', 'parentOrganFullName', 'orgOrganTypeId', 'organName', 'organCityCode', 'organLngLatBaidu', 'organLngLatHuoxing', 'organAddress', 'organRemark']
          for (const key of keys) {
            this.addForm[key] = res.data[key]
            console.log(this.addForm[key])
            if (key === 'organCityCode') {
              this.addForm[key] = String(res.data[key])
            }
          }

          // const latlng = this.addForm.organLngLatBaidu && this.addForm.organLngLatBaidu.split(',')
          // if (latlng) {
          //   reverseGeocoding(Number(latlng[0]), Number(latlng[1])).then(res => {
          //     this.addForm.organAddress = res.result.formatted_address
          //   }).catch(() => {})
          // }

          if (this.addForm.organLngLatBaidu) {
            this.activityLatLngBaidu = {
              lng: Number(this.addForm.organLngLatBaidu.split(',')[0]),
              lat: Number(this.addForm.organLngLatBaidu.split(',')[1])
            }
          } else {
            this.activityLatLngBaidu = {}
          }
        })
      } catch (e) {
        this.$errorHandle(e)
      }
    },
    // 删除组织
    remove(node, data) {
      console.log(node, '????')
      const parentNodeId = node.parent.data.orgOrganId // 保存父节点的标识符
      // // 获取父节点
      // if (node.parentOrgOrganId) {
      //   this.parentNode = this.$refs.tree.getNode(node.parentOrgOrganId)
      // }
      console.log(this.parentNode, 'parentNode')
      this.$confirm(`是否删除组织:${data.organName}?`, '提示', {
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        deleteOrganByOrganId({
          orgOrganId: data.orgOrganId
        }).then((d) => {
          this.$message.success('删除成功!')
          load.close()
          this.getTree(2, parentNodeId)
        }).catch((e) => {
          load.close()
          this.$errorHandle(e)
        })
      }).catch(() => {

      })
    },

    // 添加组织
    addSubmit() {
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          const load = this.$load()
          addOrgan({
            ...this.addForm,
            status: undefined,
            orgOrganId: undefined,
            parentOrganFullName: undefined,
            orgOrganTypeName: undefined
          }).then((d) => {
            this.getTree(1, this.busOrganObj)
            this.getInfo()
            load.close()
            this.$message.success('添加成功!')
            this.addVisible = false
          }).catch((e) => {
            load.close()
            this.$errorHandle(e)
          })
        } else {
          this.$message.error('必填项内容不允许为空，请检查')
          return false
        }
      })
    },
    // 编辑组织
    updateSubmit() {
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          const load = this.$load()
          updateOrganCommit({
            ...this.addForm,
            status: undefined,
            parentOrgOrganId: this.busOrganObj.parentOrgOrganId,
            parentOrganFullName: undefined,
            orgOrganTypeId: undefined,
            orgOrganTypeName: undefined,
            organLngLatHuoXing: this.addForm.organLngLatHuoxing,
            organLngLatHuoxing: undefined
          })
            .then((d) => {
              this.getTree(1, this.busOrganObj)
              this.getInfo()
              load.close()
              this.$message.success('修改成功!')
              this.addVisible = false
            })
            .catch((e) => {
              load.close()
              this.$errorHandle(e)
            })
        } else {
          this.$message.error('必填项内容不允许为空，请检查')
          return false
        }
      })
    }

  }
}
</script>
<style>
/*.el-tooltip__popper.is-light[x-placement^=top] .popper__arrow{*/
/*  border-top-color: #D9D9D9;*/
/*}*/
/*!* 设置箭头颜色 *!*/
/*.el-tooltip__popper[x-placement^="top"] .popper__arrow {*/
/*  border-right-color: transparent !important;*/
/*}*/
</style>
<style lang="scss" scoped>

.box-card {

  height: calc(100vh - 90px);
  user-select: none;
}
.custom-tree-node {
  //flex: 1;
  width: 100%;
  display: flex;
  align-items: center;
  //justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .edit {
    //opacity: 0;
    transition: 300ms;
    width: max-content;
    //visibility: hidden;
    &.on {
      //opacity: 1;
      //visibility: visible;
    }
    .el-button+.el-button {
        margin-left: 0px;
        padding-left: 5px;
      }
  }
}
.custom-title {
  text-align: center;
  .t1 {
    font-size: 16px;
    margin: 0;
  }
  .t2 {
    font-size: 14px;
    color: #999;
    margin: 10px auto 0;
  }
}
.dialog-footer {
  text-align: center;
}
.isOnly {
  color: rgba(0, 0, 0, 0.25);
  // cursor: not-allowed;
}
.um-info_box{
  line-height: 16px;
}

.m-card-tit{
  font-size: 16px;
  font-weight: bold;
}
.m-title{
  margin-top: 24px;
}
.dib{
  flex: 1;
  //width: 0;
  font-size: 14px;
  line-height:28px ;
  //overflow: hidden;
  //text-overflow: ellipsis;
  //white-space: nowrap;
}
.dib:hover {
  background-color: #ebf5fb;
}
.m_tree_l{
  height: calc(100% - 35px);
  width: 100%;
  overflow: auto;
  padding-top: 20px;
  position: relative;
  z-index: 999;
}
::v-deep .el-tree-node>.el-tree-node__children{
  overflow: visible;
}
.img-icon{
  width: 16px;
  height: 16px;
  //transform: rotate(90deg);
}
::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
  display: inline-flex;
  min-width: 100%;
}
.btn-box{
  width: 104px;
  .btn-item{
    height: 32px;
    line-height: 32px;
    text-align: center;
    cursor: pointer;color: #0081CC;
    color: #595959;
    &:hover{
      background-color: #E5F2F9;
      color: #0081CC;
    }
  }
}
::v-deep .el-popover{
  border: none;
}
 .popper-class{
  border: none !important;
  padding: 0 !important;
}
</style>


<template>
  <div>
    <el-form
      ref="addForm"
      class="um-add_form"
      :model="addForm"
      label-position="top"
    >
      <el-row :gutter="30" type="flex" class="f-flex-wrap">
        <el-col :span="12">
          <el-form-item
            ref="batchName"
            label="计划名称"
            prop="batchName"
            :rules="[{ required: true, message: '必填项不能为空' ,trigger: 'change'},{ min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'change' }]"
          >
            <el-input
              v-model="addForm.batchName"
              :disabled="!!isSubscribe"
              placeholder="请输入计划名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            ref="busProjectId"
            label="关联项目"
            prop="busProjectId"
            :rules="[{ required: true, message: '必填项不能为空' ,trigger: 'change'}]"
          >
            <um-bus-project
              v-model="addForm.busProjectId"
              placeholder="请选择项目"
              class="full-width"
              disabled
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item
            ref="scheduleAddress"
            label="项目地址"
            prop="scheduleAddress"
            class="item-adress"
            :rules="[{ required: true, message: '必填项不能为空' ,trigger: 'change'},{ min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'change' }]"
          >
            <el-input
              v-model="addForm.scheduleAddress"
              :disabled="!!isSubscribe"
              placeholder="请从右侧选择地图位置"
              style="width: 450px"
            />
            <el-button type="primary" class="f-ml12" @click="choosePosition">选择位置</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            ref="date"
            label="开放日期"
            prop="date"
            :rules="[{ required: true, message: '必填项不能为空' ,trigger: 'change'}]"
          >
            <el-date-picker
              v-model="addForm.date"
              type="datetimerange"
              :disabled="!!isSubscribe"
              range-separator="-"
              value-format="yyyy-MM-dd HH:mm"
              format="yyyy-MM-dd HH:mm"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="full-width"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            ref="yyTime"
            label="预约周期"
            prop="yyTime"
            :rules="[{ required: true, message: '必填项不能为空' ,trigger: 'change'}]"
          >
            <el-date-picker
              v-model="addForm.yyTime"
              type="datetimerange"
              range-separator="-"
              value-format="yyyy-MM-dd HH:mm"
              format="yyyy-MM-dd HH:mm"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="full-width"
              @change="pickerChange"
            />
            <!--            <el-date-picker-->
            <!--              v-model="addForm.scheduleLastTime"-->
            <!--              type="datetime"-->
            <!--              :disabled="!!isSubscribe"-->
            <!--              class="full-width"-->
            <!--              placeholder="选择预约截止时间"-->
            <!--            />-->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发送小程序消息">
            <template #label>
              <span>发送小程序消息：</span>
              <span class="f-ml5" style="color: rgb(140, 140, 140)">（如开启计划发布后给业主发小程序消息，反之不发送！）</span>
            </template>
            <el-radio-group v-model="addForm.pushWxAppMessageFlag">
              <el-radio :label="1">开启</el-radio>
              <el-radio :label="0">关闭</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            ref="buildingUnit"
            label="楼栋房间"
            prop="buildingUnit"
            :rules="[ { required: true, message: '必填项不能为空' ,trigger: 'change'}]"
          >
            <div style="float: right;margin-top: -35px">
              <el-button type="primary" icon="um_iconfont um_icon-export" @click="exportHandler">下载模板</el-button>
              <el-button type="primary" icon="" @click="importHandler">导入数据</el-button>
            </div>
            <um-choose-unit
              ref="umChooseUnit"
              v-model="addForm.buildingUnit"
              :is-table="false"
              class="full-width"
              :disabled="!!isSubscribe"
              :batch-type="4"
              :bus-project-id="addForm.busProjectId"
              placeholder="请选择房产"
              @change="changeRoomList"
              @choseHouseChild="choseHouseChild"
            />
            <el-table class="f-mt10" :data="buildingUnit" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="45" align="center" />
              <el-table-column label="楼栋-单元" prop="buildingUnitName" />
              <el-table-column label="房间">
                <template slot-scope="scope">
                  <div class="color-primary m_cursor" @click="choseHouse(scope.row,scope.$index)">
                    <template v-if="scope.row.roomRange===1">全部房间</template>
                    <template v-else>{{ scope.row.busBuildingRoomId.length }}个房间</template>
                    <i class="el-icon-edit f-ml8" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="验房工程师">
                <template #header="scoped">
                  <div>
                    <span class="color-danger f-mr5">*</span><span>验房工程师</span>
                    <el-tooltip placement="top" effect="light" content="在验房日期内对负责的房间进行验房操作，超出验房日期后不可验房" class="item">
                      <i class="el-icon-warning-outline f-pointer" style="margin-left:5px;" />
                    </el-tooltip><el-button type="text" @click="allChangeBusUser">批量设置</el-button>
                    <el-button type="text" @click="addBusUser">追加设置</el-button>
                  </div>
                </template>
                <template #default="{ row, $index}">
                  <um-bus-user
                    v-model="row.busUser"
                    class="bus-user"
                    placeholder="请选择验房工程师"
                    :limit="0"
                    @changeItem="val=>changeBusUser(val)"
                  >
                    <template #user="user">
                      <div
                        v-for="(item, index) in user.data"
                        :key="index"
                        style="display: inline-block; float: left; color: #0081cc;"
                      >
                        <span style="padding: 0 5px">{{ item.userName }}</span>
                      </div>
                      <i v-if="user.data&&user.data.length" class="el-icon-edit f-ml8" style="color: #0081cc" />
                    </template>
                  </um-bus-user>
                </template>
              </el-table-column>
              <el-table-column width="100" label="操作">
                <template slot-scope="scope">
                  <el-button v-if="!isSubscribe" type="text" style="color: #ef2b2b" @click="delBuildingUnit(scope.$index)">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            ref="schedulePersonInfo"
            label="人数限额设置"
            prop="schedulePersonInfo"
            :rules="[{ required: true, trigger: 'change', validator: validateTime}]"
          >
            <PersonLimit :disabled="!!isSubscribe" :value="addForm.schedulePersonInfo" :date="allDate" @change="changePersonInfo" />
            <!-- {{ addForm.schedulePersonInfo }} -->
          </el-form-item>
        </el-col>
        <!--        <el-col :span="12">-->
        <!--          <el-form-item-->
        <!--            ref="busUser"-->
        <!--            label="验房工程师"-->
        <!--            prop="busUser"-->
        <!--            :rules="[ { required: true, message: '必填项不能为空' ,trigger: 'change'}]"-->
        <!--          >-->
        <!--            &lt;!&ndash;              {{ busUser }}&ndash;&gt;-->
        <!--            <um-bus-user-->
        <!--              v-model="addForm.busUser"-->
        <!--              style="width: 100%; display: block; overflow: hidden"-->
        <!--              placeholder="请选择验房工程师"-->
        <!--              :limit="0"-->
        <!--              @changeItem="val=>changeBusUser(val)"-->
        <!--            >-->
        <!--              <template #user="user">-->
        <!--                <div-->
        <!--                  v-for="(item, index) in busUser"-->
        <!--                  :key="index"-->
        <!--                  style="display: inline-block; float: left; color: #0081cc;"-->
        <!--                >-->
        <!--                  <span style="padding: 0 5px">{{ item.userName }}({{ item.buildingUnitRange===1?'全部':item.busBuildingUnitId.length }})</span>-->
        <!--                </div>-->
        <!--              </template>-->
        <!--            </um-bus-user>-->
        <!--            <el-button type="primary" style="margin-top: 10px;" @click="()=>setHandler(busUser,1)">设置责任范围</el-button>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="12">
          <el-form-item
            ref="subHouseInspectRoleIdList"
            prop="subHouseInspectRoleIdList"
            :rules="[ { required: true, message: '必填项不能为空' ,trigger: 'change'}]"
          >
            <template slot="label">
              后续验房工程师：<el-tooltip placement="top" effect="light" content="任意时刻均可对房间进行验房操作，超出验房日期后也可验房" class="item">
                <i class="el-icon-warning-outline f-pointer" style="margin-left:5px;" />
              </el-tooltip>
            </template>
            <el-select-tree
              v-model="addForm.subHouseInspectRoleIdList"
              placeholder="请选择后续验房工程师"
              filterable
              multiple
              :options="roleTreeData"
              style="width: 100%;"
              :props="{ value: 'orgRoleId', label: 'roleName', children: 'children',checkStrictly: true }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            ref="questionRetestRoleIdList"
            prop="questionRetestRoleIdList"
            :rules="[ { required: true, message: '必填项不能为空' ,trigger: 'change'}]"
          >
            <template slot="label">
              问题复验人：<el-tooltip placement="top" effect="light" content="施工单位完成整改后，对施工单位的整改问题进行复验操作" class="item">
                <i class="el-icon-warning-outline f-pointer" style="margin-left:5px;" />
              </el-tooltip>
            </template>
            <el-select-tree
              v-model="addForm.questionRetestRoleIdList"
              placeholder="请选择问题复验人"
              filterable
              multiple
              :options="roleTreeData"
              style="width: 100%;"
              :props="{ value: 'orgRoleId', label: 'roleName', children: 'children',checkStrictly: true }"
            />
          </el-form-item>
        </el-col>
        <!--        <el-col :span="12">-->
        <!--          <el-form-item-->
        <!--            ref="fybusUser"-->
        <!--            label="后续处理人"-->
        <!--            prop="fybusUser"-->
        <!--            :rules="[ { required: true, message: '必填项不能为空' ,trigger: 'change'}]"-->
        <!--          >-->
        <!--            <um-bus-user-->
        <!--              v-model="addForm.fybusUser"-->
        <!--              style="width: 100%; display: block; overflow: hidden"-->
        <!--              placeholder="请选择后续处理人"-->
        <!--              :limit="0"-->
        <!--              @changeItem="val=>changeFybusUser(val)"-->
        <!--            >-->
        <!--              <template #user="user">-->
        <!--                <div-->
        <!--                  v-for="(item, index) in fybusUser"-->
        <!--                  :key="index"-->
        <!--                  style="display: inline-block; float: left; color: #0081cc;"-->
        <!--                >-->
        <!--                  <span style="padding: 0 5px">{{ item.userName }}({{ item.buildingUnitRange===1?'全部':item.busBuildingUnitId.length }})</span>-->
        <!--                </div>-->
        <!--              </template>-->
        <!--            </um-bus-user>-->
        <!--            <el-button type="primary" style="margin-top: 10px;" @click="()=>setHandler(fybusUser,2)">设置责任范围</el-button>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="24">
          <el-form-item
            ref="scheduleCoverFilePath"
            label="封面图片"
            prop="scheduleCoverFilePath"
            :rules="[{ required: true, message: '必填项不能为空' ,trigger: 'change'}]"
          >
            <um-upload-img-oss v-model="addForm.scheduleCoverFilePath" :module="UPLOAD_MODULE_CODE.delivery" :disabled="!!isSubscribe" placeholder="最大5MB，建议图片尺寸 1080*640" :limit="1" simple :size="5000" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item
            ref="scheduleContactPhone"
            label="咨询电话"
            prop="scheduleContactPhone"
            :rules="[{ required: true, message: '必填项不能为空' ,trigger: 'change'},{ min: 1, max: 15, message: '长度在 1 到 15 个字符', trigger: 'change' }]"
          >
            <el-input
              v-model="addForm.scheduleContactPhone"
              :disabled="!!isSubscribe"
              placeholder="请输入联系方式"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item
            ref="scheduleContent"
            label="详细内容"
            prop="scheduleContent"
            :rules="[{ required: true, message: '必填项不能为空' ,trigger: 'change'}]"
          >
            <um-edit-pro
              v-model="addForm.scheduleContent"
              :readonly="!!isSubscribe"
              :module="UPLOAD_MODULE_CODE.delivery"
              :height="300"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
      :visible.sync="mapVisible"
      width="840px"
      title="选择项目地址"
      append-to-body
      :destroy-on-close="true"
    >
      <um-map v-model="addForm.schedulePosition1" :readonly="false" />
      <div slot="footer" class="dialog-footer text-center">
        <el-button type="primary" @click="mapSubmit">保 存</el-button>
        <el-button @click="mapVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="设置责任范围" :visible.sync="setVisible" width="960px" append-to-body>
      <el-radio-group v-model="setIndex" style="margin-bottom: 20px;">
        <el-radio-button v-for="(item,index) in setList" :key="index" :label="index">{{ item.userName }}</el-radio-button>
      </el-radio-group>

      <template v-if="setList[setIndex]">
        <el-checkbox-group v-model="setList[setIndex].busBuildingUnitId" @change="()=>setList[setIndex].buildingUnitRange=2">
          <el-checkbox
            v-for="(item,key) in addForm.buildingUnit"
            :key="key"
            :label="item.busBuildingUnitId"
          >
            {{ item.buildingUnitName }}
          </el-checkbox>
        </el-checkbox-group>
      </template>

      <div slot="footer" class="dialog-footer text-center">
        <el-button @click="setVisible = false">取 消</el-button>
        <el-button type="primary" @click="setSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  apiCheckProjectHastWeChatMiniProgram,
  getBatchModifyInfo, gkUpdateSubmit
} from '@/api/customManage/jiaofu/reservation'
import PersonLimit from '../components/PersonLimit'
import { getDateInfo, reverseGeocoding, transformBD, changeHourMinutestr, parseTime, deepClone } from '@/utils'
import UmMap from '@/components/UmMap'
import umEditPro from '@/components/UmEditPro'
import { getRoleTreeData } from '@/api/system/organManage'
import { yfBatchGetRoomImportData } from '@/api/customManage/jiaofu/test'
import { UPLOAD_MODULE_CODE } from '@/enum'
const validateTime = (rule, value, callback) => {
  const time_flag = !value.length || value.some(item => !item.startDate || !item.endDate)
  if (time_flag) {
    callback(new Error('必填项不能为空'))
  } else {
    callback()
  }
}
export default {
  name: 'JiaofuAppointmentAppointmentUpdate',
  components: {
    UmMap,
    PersonLimit,
    umEditPro
  },
  props: {
    batchId: {
      type: [Number, String],
      default: null
    },
    isSubscribe: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      UPLOAD_MODULE_CODE,
      loading: false,
      addForm: {
        batchId: null,
        batchName: '',
        busProjectId: null,
        batchType: 4,
        buildingUnit: [], // 单元列表
        schedulePosition1: {}, // 临时用的
        schedulePosition: '',
        scheduleAddress: '',
        date: [],
        scheduleBeginTime: '', // 预约周期开始
        scheduleLastTime: '', // 预约周期结束
        yyTime: [], // 预约周期
        schedulePersonInfo: [],
        scheduleContactPhone: '',
        scheduleCoverFilePath: '',
        scheduleContent: '',
        schedulePublishCode: null,
        schedulePublishTime: '',
        url: '',
        busUser: [], // 验房工程师
        subHouseInspectRoleIdList: [], // 后续验房工程师
        questionRetestRoleIdList: [], // 问题复验人
        pushWxAppMessageFlag: 1
      },
      validateTime,
      roleTreeData: [],
      allDate: [],
      mapVisible: false,
      roomInfo: [],
      url: '',
      fybusUser: [],
      busUser: [],
      scheduleId: '',
      setType: 1, // 设置责任范围 类型
      setList: [], // 设置责任范围 数组
      setIndex: 0,
      setVisible: false,
      buildingUnit: [],
      multipleSelection: []
    }
  },
  watch: {
    'addForm.date'(val) {
      if (val[0] && val[1]) {
        this.allDate = getDateInfo(val[0].split(' ')[0], val[1].split(' ')[0])
      }
    },
    buildingUnit: {
      handler(val) {
        this.addForm.buildingUnit = val
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getTree()
  },
  mounted() {
    this.getInfo()
  },
  methods: {
    pickerChange(data) {
      if (data && data.length) {
        this.addForm.scheduleBeginTime = data[0]
        this.addForm.scheduleLastTime = data[1]
      } else {
        this.addForm.scheduleBeginTime = null
        this.addForm.scheduleLastTime = null
      }
    },
    // 导入数据
    importHandler() {
      this.$importFile({
        limit: 1, // 上传限制
        size: 50000, // 文件大小 kb
        progressParamData: {
          busProjectId: this.addForm.busProjectId
        },
        progressBusinessCode: 1209851,
        accept: '.xls,.xlsx'
      }).then(res => {
        this.getHoseData(res.progressId)
      }).catch((e) => {
        this.$errorHandle(e)
      })
    },
    getHoseData(progressId) {
      // 导入回显数据
      yfBatchGetRoomImportData({ progressId }).then(res => {
        this.addForm.buildingUnit = res.data.map(item => {
          return {
            ...item,
            busBuildingUnitId: item.buildingUnitId
          }
        })
        this.buildingUnit = deepClone(this.addForm.buildingUnit)
        this.buildingUnit.forEach(item => {
          this.$set(item, 'busUser', [])
          this.busUser.forEach(it => {
            if (it.busBuildingUnitId.includes(item.buildingUnitId)) {
              item.busUser.push(it.busUserId)
            }
          })
        })
      }).catch(e => {

      })
    },
    // 导出数据
    exportHandler() {
      this.$exportFile({
        isPdf: false, // 是否导出PDF
        progressParamData: {
          busProjectId: this.addForm.busProjectId
        }, // 额外数据
        progressBusinessCode: 1209850 // 业务code
      })
    },
    init(list) {
      list.forEach((item) => {
        if (item.roleTypeCode === 1003501) {
          item.disabled = true
        }
        if (item.children && item.children.length > 0) {
          this.init(item.children)
        }
      })
    },
    // 获取角色
    getTree() {
      getRoleTreeData({}).then(d => {
        const list = d.data
        this.init(list)
        this.roleTreeData = list
      }).catch(e => {
        this.$errorHandle(e)
      })
    },
    choosePosition() {
      if (!this.isSubscribe) {
        // this.mapVisible = true
        this.openMap()
      }
    },
    // 选择楼栋房间后责任范围设为全部
    changeRoomList() {
      this.buildingUnit = this.addForm.buildingUnit
      this.buildingUnit.forEach(item => {
        if (!('busUser' in item)) {
          this.$set(item, 'busUser', [])
        }
      })
    },
    choseHouse(row, index) {
      console.log(row, 'row')
      // if (this.isSubscribe) {
      //   return
      // }
      this.$refs.umChooseUnit.showRoom(row, true, this.isSubscribe)
    },
    choseHouseChild(val) {
      this.buildingUnit.forEach(item => {
        val.forEach(it => {
          if (item.busBuildingUnitId === it.busBuildingUnitId) {
            item.busBuildingRoomId = it.busBuildingRoomId
            item.roomRange = it.roomRange
          }
        })
      })
      console.log(this.buildingUnit)
    },
    // 验房工程师 选人变化后责任范围设为全部
    changeBusUser(val) {
      this.busUser = val.map(item => {
        return { ...item, buildingUnitRange: 1, busBuildingUnitId: this.buildingUnit.map(item => item.busBuildingUnitId) }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log(val, '多选')
    },
    allChangeBusUser(e) {
      if (this.multipleSelection.length === 0) {
        e.stopPropagation()
        return this.$message.warning('请先选择需要批量设置验房工程师的楼栋单元！')
      }
      this.$selectUser({
        limit: 0,
        selectMode: 'multiple',
        callback: (data, list) => {
          console.log(data, list)
          this.busUser = data.map(item => {
            return { ...item, buildingUnitRange: 1, busBuildingUnitId: this.buildingUnit.map(item => item.busBuildingUnitId) }
          })
          this.buildingUnit.forEach(it => {
            this.multipleSelection.forEach(item => {
              if (item.busBuildingUnitId === it.busBuildingUnitId) {
                it.busUser = list
              }
            })
          })
        }
      })
    },
    addBusUser(e) {
      if (this.multipleSelection.length === 0) {
        e.stopPropagation()
        return this.$message.warning('请先选择需要批量追加验房工程师的楼栋单元！')
      }
      this.$selectUser({
        limit: 0,
        selectMode: 'multiple',
        callback: (data, list) => {
          console.log(data, list)
          this.busUser = data.map(item => {
            return { ...item, buildingUnitRange: 1, busBuildingUnitId: this.buildingUnit.map(item => item.busBuildingUnitId) }
          })
          this.buildingUnit.forEach(it => {
            this.multipleSelection.forEach(item => {
              if (item.busBuildingUnitId === it.busBuildingUnitId) {
                // it.busUser = list
                it.busUser = [...new Set([...it.busUser, ...list])]
              }
            })
          })
          console.log(this.buildingUnit, ' this.buildingUnit')
        }
      })
    },
    delBuildingUnit(index) {
      this.buildingUnit.splice(index, 1)
      this.addForm.buildingUnit = this.buildingUnit
    },

    setSubmit() {
      if (this.setType === 1) {
        this.busUser = this.setList
      } else {
        // this.fybusUser = this.setList
      }
      this.setVisible = false
    },
    // 设置责任范围
    /**
     *
     * @param users
     * @param type 1 验房工程师 2 后续处理人
     * @returns {ElMessageComponent}
     */
    setHandler(users, type) {
      if (this.addForm.buildingUnit.length === 0) {
        return this.$message.error('请选择楼栋房间!')
      }
      this.setType = type
      this.setIndex = 0
      // 为了处理先选人再选房间 导致责任范围下的人的房产未选中问题
      const flag = users?.some(item => item.busBuildingUnitId && item.busBuildingUnitId.length !== 0)
      if (!flag) {
        users.forEach(item => {
          item.busBuildingUnitId = this.addForm.buildingUnit.map(item => item.busBuildingUnitId)
        })
      }
      this.setList = deepClone(users)
      this.setVisible = true
    },
    changePersonInfo(data) {
      this.addForm.schedulePersonInfo = data
    },
    openMap() {
      console.log(this.addForm, 'snzk')
      this.$locationTDT({
        value: {
          formatted_address: this.addForm.schedulePosition ? this.addForm.scheduleAddress : '',
          lng: this.addForm.schedulePosition ? Number(this.addForm.schedulePosition.split(',')[0]) : '',
          lat: this.addForm.schedulePosition ? Number(this.addForm.schedulePosition.split(',')[1]) : ''
        },
        resultHandler: (position) => {
          this.addForm.scheduleAddress = position.formatted_address
          const huoxing = position.gcj02_lng ? `${position.gcj02_lng},${position.gcj02_lat}` : ''
          const baidu = position.lng ? `${position.lng},${position.lat}` : ''
          this.addForm.schedulePosition = baidu + ',' + huoxing
        }
      })
    },
    async mapSubmit() {
      const latlng = this.addForm.schedulePosition1
      if (!latlng.lat || !latlng.lng) {
        this.$message.info('请选择位置！')
        return false
      }
      try {
        const d2 = await reverseGeocoding(latlng.lng, latlng.lat)
        this.addForm.scheduleAddress = d2.result.formatted_address
      } catch (e) {
        this.$message.error('获取位置失败！')
        return false
      }
      let organLngLatHuoxing = ''
      let organLngLatBaidu = ''
      try {
        const d = await transformBD(latlng.lng, latlng.lat, 'gcj02')
        if (d.result && d.result[0]) {
          organLngLatHuoxing = `${d.result[0].x},${d.result[0].y}`
          organLngLatBaidu = `${latlng.lng},${latlng.lat}`
        } else {
          this.$message.error('获取经纬度失败！')
        }
      } catch (e) {
        this.$message.error('获取经纬度失败！')
        return false
      }
      this.addForm.schedulePosition = organLngLatBaidu + ',' + organLngLatHuoxing
      this.mapVisible = false
    },
    submit(callBack) {
      this.$refs.addForm.validate(async valid => {
        if (valid) {
          const load = this.$load()
          try {
            const schedulePersonInfo = []
            this.addForm.schedulePersonInfo.forEach(i => {
              const obj = { personNum: [] }
              // if (!i.date || i.date.length !== 2) {
              //   this.$message.warning('请选择预约时段')
              //   load.close()
              //   throw new Error('错误')
              // }
              if (!i.startDate || !i.endDate) {
                this.$message.warning('请选择预约时段')
                throw new Error('错误')
              }
              // obj.dateBeginTime = i.date[0]
              obj.dateBeginTime = i.startDate
              // obj.dateEndTime = i.date[1]
              obj.dateEndTime = i.endDate
              this.allDate.forEach(i1 => {
                if (i[i1] !== null) {
                  obj.personNum.push(i[i1])
                }
              })
              schedulePersonInfo.push(obj)
            })
            const BUSUSER_MAP = {} // 按照人员归组
            this.buildingUnit.forEach(item => { // 循环表格数据
              const busUser = item.busUser
              const buildingUnitId = item.busBuildingUnitId
              if (Array.isArray(busUser)) {
                busUser.forEach(user => { // 遍历每行的人员数据，人员当key,单元ID为值里面的busBuildingUnitId数组
                  if (BUSUSER_MAP[user]) {
                    BUSUSER_MAP[user].busBuildingUnitId.push(buildingUnitId)
                  } else {
                    BUSUSER_MAP[user] = { busBuildingUnitId: [buildingUnitId] }
                  }
                })
              }
            })
            for (const key in BUSUSER_MAP) { // 遍历 人员map,添加是否管理所有的单元，以及默认role字段
              const item = BUSUSER_MAP[key]
              if (item.busBuildingUnitId.length === this.buildingUnit.length) {
                item.buildingUnitRange = 1
              } else {
                item.buildingUnitRange = 2
              }
              item.role = '1'
            }

            const busUser = []
            Object.keys(BUSUSER_MAP).forEach((busUserId, index) => {
              busUser.push({ ...BUSUSER_MAP[busUserId], busUserId: busUserId })
            })
            const flag = this.buildingUnit.every(item => item.busUser.length !== 0)
            if (Object.keys(BUSUSER_MAP).length === 0 || !flag) {
              load.close()
              return this.$message.warning('请选择验房工程师')
            }
            const beginTime = this.addForm.date ? new Date(this.addForm.date[0]) : null
            const endTime = this.addForm.date ? new Date(this.addForm.date[1]) : null
            const obj = {
              batchId: this.batchId,
              batchType: 4,
              vacantFlag: window.buildingType2 !== 1,
              batchName: this.addForm.batchName,
              busProjectId: this.addForm.busProjectId,
              scheduleAddress: this.addForm.scheduleAddress,
              schedulePosition: this.addForm.schedulePosition,
              beginTime: beginTime ? parseTime(beginTime, '{y}-{m}-{d} {h}:{i}:{s}') : null,
              endTime: endTime ? parseTime(endTime, '{y}-{m}-{d} {h}:{i}:{s}') : null,
              scheduleLastTime: this.addForm.scheduleLastTime ? parseTime(this.addForm.scheduleLastTime, '{y}-{m}-{d} {h}:{i}:{s}') : null,
              scheduleBeginTime: this.addForm.scheduleBeginTime ? parseTime(this.addForm.scheduleBeginTime, '{y}-{m}-{d} {h}:{i}:{s}') : null,
              scheduleContactPhone: this.addForm.scheduleContactPhone,
              scheduleCoverFilePath: this.addForm.scheduleCoverFilePath,
              busBuildingRoomId: this.addForm.busBuildingRoomId,
              scheduleContent: this.addForm.scheduleContent,
              buildingUnit: this.buildingUnit.map(item => {
                return { busBuildingId: item.busBuildingId, busBuildingUnitId: item.busBuildingUnitId, roomRange: item.roomRange, busBuildingRoomId: item.buildingUnitRange === 1 ? [] : item.busBuildingRoomId }
              }),
              busUser: busUser,
              //     this.busUser.map(item => {
              //   return { busUserId: item.orgUserId, buildingUnitRange: item.buildingUnitRange, role: '1', busBuildingUnitId: item.busBuildingUnitId }
              // }),
              subHouseInspectRoleIdList: this.addForm.subHouseInspectRoleIdList,
              questionRetestRoleIdList: this.addForm.questionRetestRoleIdList,
              // fybusUser: this.fybusUser.map(item => {
              //   return { busUserId: item.orgUserId, buildingUnitRange: item.buildingUnitRange, role: '2', busBuildingUnitId: item.busBuildingUnitId }
              // }),
              schedulePersonInfo: schedulePersonInfo,
              pushWxAppMessageFlag: this.addForm.pushWxAppMessageFlag
            }
            let content
            // 判断项目是否为绑定小程序
            const { data } = await apiCheckProjectHastWeChatMiniProgram({ busProjectId: this.addForm.busProjectId })
            if (data) {
              content = '您确认要发布该计划吗，发布后可在已发布计划中查看该数据！'
            } else {
              content = `<div style="color: red;">提示：该项目暂未绑定小程序，计划发布后业主无法收到小程序的工地开放通知!</div>
  <div>您确认要发布该计划吗，发布后可在已发布计划中查看该数据!</div>`
            }
            this.$confirm(content, '发布计划', {
              type: 'warning',
              dangerouslyUseHTMLString: true // 允许解析 HTML 字符串
            }).then(async d => {
              const load = this.$load()
              try {
                await gkUpdateSubmit({
                  ...obj
                })
                this.$alert('保存成功', '提示', {
                  confirmButtonText: '确定',
                  showClose: false,
                  type: 'success'
                }).then((d) => {
                  this.$emit('submit')
                  load.close()
                  callBack(true)
                }).catch(e => {

                })
                load.close()
              } catch (e) {
                load.close()
                this.$errorHandle(e)
              }
            }).catch(e => {
              console.log(e)
              callBack(false)
            })
          } catch (e) {
            callBack(false)
            load.close()
            this.$errorHandle(e)
          }
          load.close()
        } else {
          callBack(false)
          console.log('校验没过')
        }
      })
    },
    async getInfo() {
      try {
        const res = await getBatchModifyInfo({ batchId: this.batchId })
        const { yfBusUserList, fybusUserList, yfBuildingUnitList } = res.data.info
        console.log(res.data, '3333')
        this.addForm.scheduleAddress = res.data.info.scheduleAddress // 地址
        this.addForm.busProjectId = res.data.info.projectId // 项目ID
        this.addForm.batchName = res.data.info.batchName // 计划名称
        this.$refs.umChooseUnit.type = res.data.info.vacantFlag ? 2 : 1
        this.addForm.date = [res.data.info.beginTime, res.data.info.endTime] // 时间
        this.addForm.batchDescription = res.data.info.batchDescription // 描述
        this.addForm.scheduleLastTime = res.data.info.scheduleLastTime
        this.addForm.scheduleBeginTime = res.data.info.scheduleBeginTime
        this.addForm.yyTime = [this.addForm.scheduleBeginTime, this.addForm.scheduleLastTime]
        this.addForm.schedulePosition = res.data.info.schedulePosition
        // this.addForm.schedulePosition1 = { lng: res.data.info.schedulePosition.split(',')[0], lat: res.data.info.schedulePosition.split(',')[1] }
        // if (this.addForm.schedulePosition1) {
        //   this.mapSubmit()
        // }
        this.addForm.scheduleCoverFilePath = res.data.info.scheduleCoverFileHttpPath
        this.addForm.scheduleContactPhone = res.data.info.scheduleContactPhone
        this.addForm.scheduleContent = res.data.info.scheduleContentHttpPath
        this.addForm.pushWxAppMessageFlag = res.data.info.pushWxAppMessageFlag
        const arr = []// 人员限制
        res.data.info.dateInfo.forEach(i1 => {
          i1.personNum = i1.personNum?.split(',')
          const obj = {}
          getDateInfo(res.data.info.beginTime?.split(' ')[0], res.data.info.endTime?.split(' ')[0]).forEach((i2, idx2) => {
            // obj.date = [changeHourMinutestr(i1.dateBeginTime), changeHourMinutestr(i1.dateEndTime)]
            obj.startDate = changeHourMinutestr(i1.dateBeginTime)
            obj.endDate = changeHourMinutestr(i1.dateEndTime)
            i1.personNum.forEach((i3, idx3) => {
              if (idx3 === idx2) {
                obj[i2] = i3
              }
            })
          })
          arr.push(obj)
        })
        this.addForm.schedulePersonInfo = arr
        this.addForm.busUser = yfBusUserList.map(i => i.busUserId)
        // this.addForm.fybusUser = fybusUserList.map(i => i.busUserId)
        this.addForm.subHouseInspectRoleIdList = res.data.info.subHouseInspectRoleIdList
        this.addForm.questionRetestRoleIdList = res.data.info.questionRetestRoleIdList
        this.busUser = yfBusUserList.map(item => {
          return {
            ...item,
            buildingUnitRange: item.busBuildingUnitId.length == yfBuildingUnitList.length ? 1 : 2,
            orgUserId: item.busUserId,
            userName: item.batchUserName
          }
        })
        this.addForm.buildingUnit = yfBuildingUnitList.map(item => {
          return {
            ...item,
            busBuildingUnitId: item.buildingUnitId
          }
        })
        this.buildingUnit = deepClone(this.addForm.buildingUnit)
        this.buildingUnit.forEach(item => {
          this.$set(item, 'busUser', [])
          this.busUser.forEach(it => {
            if (it.busBuildingUnitId.includes(item.buildingUnitId)) {
              item.busUser.push(it.busUserId)
            }
          })
        })
        console.log(this.buildingUnit, '/////////////')
      } catch (e) {
        this.$errorHandle(e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table td.is-hidden > *{
  visibility:visible !important;
}
::v-deep .item-adress .el-form-item__content{
  display: flex;
}
::v-deep{
  .pic_txt{
    width: 520px !important;
  }
}
.bus-user{
  ::v-deep .el-button{
    width: 100%;
    background: #fff;
    border: 1px solid #eee;
    text-align: left;
    height: 32px;
    padding: 0 0 0 12px;
    span{
      font-size: 14px !important;
    }
    .el-tag--small{
      margin: 0 !important;
    }
  }
}
</style>

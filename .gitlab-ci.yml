variables:
  # 防止GITlab-Runner 拉取代码失败
  GIT_STRATEGY: clone
  # 企微机器人链接
  SONARQUBE_WEBHOOK_URL: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9ae29fe3-19f5-4737-9236-5e4115fd240c"
  # sonarqube 项目Key
  SONARQUBE_PROJECT_KEY: "cscec-kfwy_web_pc_AY7qpV24UNhnk9Z6s1ar"
sonarqube-check:
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner -Dsonar.projectKey=$SONARQUBE_PROJECT_KEY -Dsonar.qualitygate.wait=true -Dsonar.exclusions=node_modules/**,dist/**,public/**
  allow_failure: true
  only:
    - main
sonarqube-push:
  script:
    - wget -O - ************:8100/software-command/sonarqube/sonarqube-push.sh |  bash
